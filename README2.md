# AI Coder's Quick Reference - HVAC Topology Orchestrator

## 🏗️ Architecture Overview
**Core Principle**: "State lives in hooks; App orchestrates."
- `App.tsx` is the central orchestrator managing all state and coordinating components
- Custom hooks handle specific functionality (`useChatbot`, `useGoJSLoader`)
- Components are primarily presentational with minimal local state

## 📊 Data Flow & Types
The app works with 4 main data structures:
1. **MasterTopology** - Physical/logical equipment layout
2. **ValidationReportData** - System validation results  
3. **ControlReadinessSummaryData** - Control capability metrics
4. **OntologyGraph** - Conceptual system relationships

All stored in `App.tsx` state and passed down to components.

## 🧠 AI Integration (Gemini)
- **Chatbot**: Natural language UI control + system analysis
- **File Validation**: AI-powered JSON structure validation
- **Action Commands**: AI can execute UI actions via JSON responses
- **System Instructions**: Context-aware prompts using all 4 data models

## 🎯 Key Features
- **Dynamic Data Upload**: Replace hardcoded data via `DataUploader` component
- **Interactive Diagram**: GoJS-powered topology visualization
- **AI-Driven Analysis**: 4 instant AI prompts for system insights
- **Path Tracing**: <PERSON><PERSON>stra algorithm for fault path visualization
- **Multi-Modal Views**: Dashboard, reports, summaries

## 📁 Critical Files
- `App.tsx` - Main orchestrator, all state management
- `services/geminiService.ts` - AI integration & validation
- `components/DataUploader.tsx` - File upload with AI validation
- `hooks/useChatbot.ts` - AI chat functionality
- `types.ts` - All TypeScript interfaces
- `utils/dataTransformer.ts` - Data processing utilities

## 🚀 Enhancement Roadmap
See `docs/ENHANCEMENTS.md` for detailed feature roadmap including:
- Proactive fault detection engine
- Historical data trending
- Comparative analysis views
- Multi-node selection

## 🔧 Development Notes
- Uses GoJS for diagram rendering
- Gemini 2.5 Flash model for AI features
- React + TypeScript
- Tailwind CSS for styling
- Environment variable: `GEMINI_API_KEY` required
