export interface Point {
    Name: string;
    Control_Layer?: 'Sensor' | 'Setpoint' | 'Command' | 'Alarm' | 'Status';
}

export interface Equipment {
    Equip_ID: string;
    Equip_Type: 'AHU' | 'VAV' | string;
    Points?: Point[];
}

export interface Zone {
    Zone_ID: string;
    Zone_Type: string;
    Criticality: 'High' | 'Medium' | 'Low' | string;
    Served_By: {
        AHU: string | null;
        Secondary: string[];
    };
    Equipment: Equipment[];
}

export interface Room {
    Room: string;
    Zones: Zone[];
}

export interface Floor {
    Floor: string;
    Rooms: Room[];
}

export interface MasterTopology {
    Building: string;
    Floors: Floor[];
    CrossRefs: {
        AHU_to_VAVs: Record<string, string[]>;
    };
}

// GoJS Diagram data types
export interface DiagramNodeData {
    key: string | number;
    name: string;
    category?: string;
    data: Record<string, any>;
}

export interface DiagramLinkData {
    from: string | number;
    to: string | number;
    category?: string;
}

// Validation Report Types
export interface ValidationIssue {
    Check: string;
    Message: string;
    Count?: number;
    Example_Assets?: string[];
    Sources?: string[];
}

export interface ValidationCheckSection {
    Status: 'Passed' | 'Warning' | 'Failed';
    Issues?: ValidationIssue[];
    Details?: string;
}

export interface ValidationSummary {
    Status: string;
    Total_Checks: number;
    Passed_Count: number;
    Warning_Count: number;
    Failed_Count: number;
    Overall_Message: string;
}

export interface ValidationReportData {
    Validation_Timestamp: string;
    Schema_Compliance: ValidationCheckSection;
    Connectivity_Checks: ValidationCheckSection;
    Point_Mapping_Checks: ValidationCheckSection;
    Data_Source_Checks: ValidationCheckSection;
    Summary: ValidationSummary;
}

// Control Readiness Summary Types
export interface InventorySummary {
    Total_Floors: number;
    Total_Zones: number;
    Total_Equipment_Assets: number;
    Total_Points: number;
}

export interface AssetDistribution {
    [key: string]: number;
}

export interface PointDistribution {
    Sensor: number;
    Actuator: number;
    Setpoint: number;
    Command: number;
    Status: number;
    Alarm: number;
}

export interface ControlCapability {
    Directly_Controllable_Zones_Temp: number;
    Directly_Controllable_Zones_Flow: number;
    Criticality_High_Zones_Count: number;
    Criticality_Medium_Zones_Count: number;
    Criticality_Low_Zones_Count: number;
}

export interface DataCompleteness {
    Assets_With_Points_Pct: number;
    Assets_With_Critical_Points_Pct: number;
    Message: string;
}

export interface ControlReadinessSummaryData {
    Summary_Timestamp: string;
    Inventory_Summary: InventorySummary;
    Asset_Distribution_By_Type: AssetDistribution;
    Point_Distribution_By_Control_Layer: PointDistribution;
    Control_Capability_Summary: ControlCapability;
    Data_Completeness: DataCompleteness;
}

// Ontology Types
export interface OntologyNode {
    id: string;
    label: string;
    type: 'Concept' | 'Goal' | 'Instance';
    description?: string;
}
export interface OntologyLink {
    source: string;
    target: string;
    label: 'is_a' | 'part_of' | 'achieves' | 'related_to';
}
export interface OntologyGraph {
    nodes: OntologyNode[];
    links: OntologyLink[];
}


// App-specific types
export type ControlStrategies = Record<string, string>;
export interface DijkstraPath {
    nodes: (string | number)[];
    links: { from: string | number; to: string | number }[];
}


// Chatbot types
export interface ChatMessage {
    role: 'user' | 'model';
    content: string;
}

// AI Service types
export interface AiActionCommand {
    action: 'SELECT_NODE' | 'SET_FILTER' | 'OPEN_DASHBOARD' | 'UPDATE_CONTROL_STRATEGY' | 'CALCULATE_DIJKSTRA_PATH';
    payload: any;
}

export type AiResponse =
    | { type: 'text'; content: string }
    | { type: 'action'; command: AiActionCommand };

export interface AiValidationResponse {
    isValid: boolean;
    message: string;
}