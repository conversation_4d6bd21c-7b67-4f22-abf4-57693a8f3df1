import type { MasterTopology, DiagramNodeData, DiagramLinkData, Point } from '../types';

export const transformTopologyToModel = (topology: MasterTopology): { nodes: DiagramNodeData[], links: DiagramLinkData[] } => {
    const nodes: DiagramNodeData[] = [];
    const links: DiagramLinkData[] = [];
    
    if (!topology || !topology.Building) {
        return { nodes, links };
    }

    const getControlLayer = (name: string): Point['Control_Layer'] => {
        const lowerName = name.toLowerCase();
        if (lowerName.includes('setpoint')) return 'Setpoint';
        if (lowerName.includes('demand')) return 'Command';
        if (lowerName.includes('fault')) return 'Alarm';
        if (lowerName.includes('status') || lowerName.includes('available')) return 'Status';
        return 'Sensor';
    };

    const buildingKey = topology.Building;
    nodes.push({ key: buildingKey, name: buildingKey, category: 'Building', data: { Name: buildingKey } });

    topology.Floors.forEach(floor => {
        const floorKey = `Floor-${floor.Floor}`;
        if (!nodes.some(n => n.key === floorKey)) {
           nodes.push({ key: floorKey, name: `Floor ${floor.Floor}`, category: 'Floor', data: { Name: floor.Floor } });
           links.push({ from: buildingKey, to: floorKey });
        }
        
        floor.Rooms.forEach(room => {
            const roomKey = `${floorKey}-${room.Room}`;
            if (!nodes.some(n => n.key === roomKey)) {
                nodes.push({ key: roomKey, name: room.Room, category: 'Room', data: { Name: room.Room } });
                links.push({ from: floorKey, to: roomKey });
            }
            
            room.Zones.forEach(zone => {
                 const zoneKey = `Zone-${zone.Zone_ID}`;
                 nodes.push({ key: zoneKey, name: zone.Zone_ID, category: 'Zone', data: zone });
                 links.push({ from: roomKey, to: zoneKey });
                 
                 zone.Equipment.forEach(equip => {
                     if (!nodes.find(n => n.key === equip.Equip_ID)) {
                         nodes.push({ key: equip.Equip_ID, name: equip.Equip_ID, category: equip.Equip_Type, data: equip });
                     }
                     links.push({ from: zoneKey, to: equip.Equip_ID });
                     
                     (equip.Points || []).forEach((point, index) => {
                          const pointKey = `${equip.Equip_ID}-${point.Name.replace(/\s+/g, '')}-${index}`;
                          nodes.push({ key: pointKey, name: point.Name, category: 'Point', data: { ...point, Control_Layer: getControlLayer(point.Name) } });
                          links.push({ from: equip.Equip_ID, to: pointKey });
                     });
                 });
            });
        });
    });

    if (topology.CrossRefs && topology.CrossRefs.AHU_to_VAVs) {
        for (const ahuId in topology.CrossRefs.AHU_to_VAVs) {
            const vavIds = topology.CrossRefs.AHU_to_VAVs[ahuId];
            vavIds.forEach(vavId => {
                 if (!nodes.find(n => n.key === vavId)) {
                     nodes.push({ key: vavId, name: vavId, category: 'VAV', data: { Equip_ID: vavId, Equip_Type: 'VAV' }});
                 }
                 links.push({ from: ahuId, to: vavId, category: 'DownstreamLink' });

                 const match = vavId.match(/VAV-(\d+)-/);
                 if (match) {
                     const floorNumStr = match[1];
                     const floorName = `L${floorNumStr.padStart(2, '0')}`;
                     let floorKey = `Floor-${floorName}`;
                     
                     if (!nodes.some(n => n.key === floorKey)) {
                         nodes.push({ key: floorKey, name: `Floor ${floorNumStr}`, category: 'Floor', data: { Name: floorNumStr } });
                         links.push({ from: buildingKey, to: floorKey });
                     }
                     
                     let vavRoomKey = `${floorKey}-VAVs`;
                     if (!nodes.some(n => n.key === vavRoomKey)) {
                        nodes.push({ key: vavRoomKey, name: `VAVs`, category: 'Room', data: { Name: 'VAVs' } });
                        links.push({ from: floorKey, to: vavRoomKey });
                     }
                     links.push({ from: vavRoomKey, to: vavId });
                 }
            });
        }
    }
    
    return { nodes, links };
};
