import type { DiagramNodeData, DiagramLinkData, DijkstraPath } from '../types';

export const calculateShortestPath = (
    nodes: DiagramNodeData[],
    links: DiagramLinkData[],
    startKey: string | number,
    endKey: string | number
): DijkstraPath | null => {
    
    const adjList = new Map<string | number, (string | number)[]>();

    nodes.forEach(node => adjList.set(node.key, []));

    links.forEach(link => {
        adjList.get(link.from)?.push(link.to);
        adjList.get(link.to)?.push(link.from); // Assuming undirected graph for pathfinding
    });

    const distances = new Map<string | number, number>();
    const prev = new Map<string | number, string | number | null>();
    const pq = new Set<string | number>();

    nodes.forEach(node => {
        distances.set(node.key, Infinity);
        prev.set(node.key, null);
        pq.add(node.key);
    });

    distances.set(startKey, 0);

    while (pq.size > 0) {
        let closestNode: string | number | null = null;
        let minDistance = Infinity;

        for (const nodeKey of pq) {
            const dist = distances.get(nodeKey)!;
            if (dist < minDistance) {
                minDistance = dist;
                closestNode = nodeKey;
            }
        }

        if (closestNode === null || closestNode === endKey) break;

        pq.delete(closestNode);

        const neighbors = adjList.get(closestNode) || [];
        for (const neighbor of neighbors) {
            if (pq.has(neighbor)) {
                const alt = distances.get(closestNode)! + 1; // Edge weight is 1
                if (alt < distances.get(neighbor)!) {
                    distances.set(neighbor, alt);
                    prev.set(neighbor, closestNode);
                }
            }
        }
    }

    // Path reconstruction
    if (prev.get(endKey) === undefined || prev.get(endKey) === null) {
        return null; // No path found
    }
    
    const pathNodes: (string | number)[] = [];
    let current: string | number | null = endKey;
    while (current !== null) {
        pathNodes.unshift(current);
        current = prev.get(current) ?? null;
    }

    if (pathNodes[0] !== startKey) return null; // Path not possible

    const pathLinks: { from: string | number; to: string | number }[] = [];
    for (let i = 0; i < pathNodes.length - 1; i++) {
        pathLinks.push({ from: pathNodes[i], to: pathNodes[i+1] });
    }
    
    return { nodes: pathNodes, links: pathLinks };
};
