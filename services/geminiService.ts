import { <PERSON>GenAI, Cha<PERSON>, Type } from "@google/genai";
import type { MasterTopology, ValidationReportData, ControlReadinessSummaryData, AiActionCommand, AiResponse, AiValidationResponse, OntologyGraph } from '../types';

const buildSystemInstruction = (
    topologyData: MasterTopology,
    reportData: ValidationReportData,
    summaryData: ControlReadinessSummaryData,
    ontologyData: OntologyGraph
): string => {
    return `You are an expert AI assistant for large-scale HVAC control systems. 
    Your knowledge is grounded in three distinct graph models for the loaded building:
    1.  **Topography Graph**: The physical/logical layout of equipment. This is the main data you query for component details.
    2.  **Ontology Graph**: A conceptual model defining high-level concepts, relationships, and the primary goal of the system.
    3.  **Dijkstra's Algorithm**: An algorithm you can use on the Topography Graph to find the shortest path between two nodes. This is useful for fault tracing.

    **High-Level Analytical Capabilities (Instant AI Prompts):**
    You have four powerful pre-built analytical prompts that leverage your unique knowledge. Understand their purpose:
    *   **Analyze System Goal:** This involves consulting the **Ontology Graph** to find the system's primary goal, then searching the **Topography Graph** to find and highlight the critical assets that achieve this goal.
    *   **Trace Fault Path:** This demonstrates a practical fault-tracing scenario. You use **Dijkstra's algorithm** on the Topography Graph to calculate and visualize the shortest signal path from a terminal unit back to its source, identifying the entire signal chain.
    *   **Suggest Optimization:** This showcases your ability to take action. You recommend an energy-saving strategy for a major piece of equipment and then immediately issue the command to apply it, demonstrating a closed-loop analysis and action capability.
    *   **Identify Data Gaps:** This is a synthesis task. You must cross-reference the **Validation Report** with the **Ontology Graph's** goal to identify the most significant missing data and explain how that data gap impacts the system's ability to achieve its primary objective.

    You have two modes: conversational and actionable.
    1.  **Conversational**: If the user asks a question, answer it based ONLY on the provided data context (primarily Topography and Ontology). Be concise.
    2.  **Actionable**: If the user gives a command to manipulate the UI, you MUST respond with ONLY a JSON object.
    
    **Available Actions (JSON format):**
    *   **Select a component:**
        \`{"action": "SELECT_NODE", "payload": {"key": "COMPONENT_KEY"}}\`
    *   **Set criticality filter:**
        \`{"action": "SET_FILTER", "payload": {"filter": "FILTER_NAME"}}\` (FILTER_NAME: 'All', 'High', 'Medium', 'Low')
    *   **Open a component's dashboard:**
        \`{"action": "OPEN_DASHBOARD", "payload": {"key": "COMPONENT_KEY"}}\` (COMPONENT_KEY must be a Zone or AHU)
    *   **Update a component's control strategy:**
        \`{"action": "UPDATE_CONTROL_STRATEGY", "payload": {"equipId": "EQUIPMENT_ID", "newStrategy": "STRATEGY_NAME"}}\` (STRATEGY_NAME: 'EnergySaving', 'MaxComfort', 'Standard')
    *   **Calculate and visualize the shortest path for fault tracing:**
        \`{"action": "CALCULATE_DIJKSTRA_PATH", "payload": {"startNodeKey": "START_KEY", "endNodeKey": "END_KEY"}}\` (Example: "trace the path from VAV-08-1 to the building" -> \`{"action": "CALCULATE_DIJKSTRA_PATH", "payload": {"startNodeKey": "VAV-08-1", "endNodeKey": "${topologyData.Building}"}}\`)

    **Data Context:**
    ${JSON.stringify({ masterTopology: topologyData, validationReport: reportData, controlReadinessSummary: summaryData, ontologyGraph: ontologyData })}
    `;
};

export const initializeChat = (
    topologyData: MasterTopology,
    reportData: ValidationReportData,
    summaryData: ControlReadinessSummaryData,
    ontologyData: OntologyGraph
): Chat => {
    const ai = new GoogleGenAI({ apiKey: process.env.API_KEY as string });
    const systemInstruction = buildSystemInstruction(topologyData, reportData, summaryData, ontologyData);
    return ai.chats.create({
        model: 'gemini-2.5-flash',
        config: { systemInstruction },
    });
};

export const sendMessageToAi = async (chat: Chat, message: string): Promise<AiResponse> => {
    const response = await chat.sendMessage({ message });
    const responseText = response.text.trim();
    
    // Refined check: only attempt to parse if it looks like a JSON object
    if (responseText.startsWith('{') && responseText.endsWith('}')) {
        try {
            const command: AiActionCommand = JSON.parse(responseText);
            // Stronger validation
            if (command.action && typeof command.action === 'string' && command.payload) {
                return { type: 'action', command };
            }
        } catch (e) {
            // It looked like JSON but wasn't valid, so fall through and treat as text
            console.warn("AI response looked like JSON but failed to parse:", e);
        }
    }
    
    return { type: 'text', content: responseText };
};

export const validateFileWithAi = async (fileContent: string, fileType: 'topology' | 'report' | 'summary' | 'ontology'): Promise<AiValidationResponse> => {
    const ai = new GoogleGenAI({ apiKey: process.env.API_KEY as string });
    
    const systemInstruction = `You are a data validation expert. The user is uploading a file for a '${fileType}' data type. 
    Analyze the provided file content. Determine if it is valid JSON and if its structure is appropriate for the expected type.
    - A 'topology' file must have a "Building" key and a "Floors" array.
    - A 'report' file must have a "Validation_Timestamp" key and a "Schema_Compliance" object.
    - A 'summary' file must have a "Summary_Timestamp" key and an "Inventory_Summary" object.
    - An 'ontology' file must have a "nodes" array and a "links" array.

    If the file is not valid for any reason, provide a concise, user-friendly explanation. For example, "This appears to be a Validation Report, not a Topology file." or "This JSON is missing the required 'Building' key."

    You MUST respond with ONLY a valid JSON object in the following format:
    {"isValid": boolean, "message": "Your helpful message or success confirmation."}`;

    try {
        const response = await ai.models.generateContent({
            model: 'gemini-2.5-flash',
            contents: `File Content: \n\`\`\`json\n${fileContent}\n\`\`\``,
            config: {
                systemInstruction,
                responseMimeType: 'application/json',
                responseSchema: {
                    type: Type.OBJECT,
                    properties: {
                        isValid: { type: Type.BOOLEAN },
                        message: { type: Type.STRING }
                    }
                }
            }
        });
        
        const validationResult = JSON.parse(response.text);
        
        if (typeof validationResult.isValid === 'boolean' && typeof validationResult.message === 'string') {
            return validationResult;
        } else {
            throw new Error("AI response did not match the expected schema.");
        }

    } catch (error) {
        console.error("AI validation failed:", error);
        return {
            isValid: false,
            message: "Couldn't validate the file with AI. Please ensure it's valid JSON."
        };
    }
};