# AI Web Developer Enhancement Roadmap

This document outlines a strategic roadmap for enhancing the HVAC Topology Orchestrator application. It is intended to be used by an AI web developer as a guide for implementing new features. All technical plans should adhere to the core architectural principle: **"State lives in hooks; App orchestrates."**

---

## 1. Data & Connectivity

### Feature 1.1: Dynamic Data Loading

*   **User Value:** Greatly increases the tool's utility by allowing users to visualize their own building data instead of relying on the hardcoded topology.
*   **Feature Description:** Implement a user interface for uploading the `Master Topology`, `Validation Report`, and `Control Readiness` JSON files. The application should parse these files and dynamically re-render the entire visualization and all report modals.
*   **Technical Plan:**
    1.  Create a new `DataUploader` component that provides a clean drag-and-drop or file selection interface.
    2.  In `App.tsx`, replace the static imports from `./data/` with `useState` hooks for `masterTopology`, `validationReport`, and `controlReadinessSummary`.
    3.  Create a handler function in `App.tsx` that takes the uploaded files, parses them, and updates these state variables.
    4.  The `useMemo` hook for `transformTopologyToModel` will automatically re-run when its `masterTopology` dependency changes, triggering a seamless re-render of the diagram.

---

## 2. AI & Intelligence

### Feature 2.1: Proactive Fault Detection Engine

*   **User Value:** Transforms the tool from a passive visualizer into an active monitoring system that automatically flags potential issues, helping operators identify problems before they escalate.
*   **Feature Description:** Use the Gemini API to analyze the "live" data from the dashboard and flag anomalies. For example, if a `Discharge_Air_Temp` is significantly different from its `Supply_Air_Temp_Setpoint`, the system should highlight the affected AHU and related components in the diagram.
*   **Technical Plan:**
    1.  Create a new `useDiagnosticsEngine` custom hook. This hook will periodically (e.g., every 10 seconds) process the "live" data points.
    2.  Within the hook, send a curated set of data points to the Gemini API with a system instruction to identify potential faults based on HVAC logic (e.g., "Return a JSON array of `Equip_ID`s where temperature deviates from setpoint by more than 3 degrees").
    3.  The hook will return an array of faulty equipment keys. In `App.tsx`, store this array in a `useState` hook (`[faultyNodes, setFaultyNodes]`).
    4.  Pass the `faultyNodes` array as a prop to the `DiagramWrapper`. The diagram component will then use this list to apply a distinct visual style (e.g., a pulsating red highlight) to the affected nodes.

### Feature 2.2: Actionable AI Chatbot

*   **User Value:** Allows users to control the application using natural language, making complex queries and navigation effortless.
*   **Feature Description:** Upgrade the chatbot so it can understand and execute commands that manipulate the UI. Examples:
    *   *"Show me AHU-4"* -> Pans/zooms to and selects AHU-4.
    *   *"Generate the dashboard for Ground-MechZone-AHU"* -> Selects the zone and opens the dashboard.
    *   *"Highlight all high criticality zones"* -> Applies the 'High' criticality filter.
*   **Technical Plan:**
    1.  In `App.tsx`, enhance the chatbot's system instruction to include a list of available actions and the JSON format for returning commands.
    2.  Configure the Gemini `generateContent` call to use JSON Output. The expected output would be an object like `{ "action": "SELECT_NODE", "payload": { "key": "AHU-4" } }` or `{ "action": "SET_FILTER", "payload": { "filter": "High" } }`.
    3.  The `handleSendMessage` function in `App.tsx` will parse the AI's JSON response. A `switch` statement will then call the appropriate state setter function (`handleNodeSelection`, `setCriticalityFilter`, `setIsDashboardOpen`, etc.) based on the `action` received. This perfectly aligns with the orchestration principle.

---

## 3. Visualization & UX

### Feature 3.1: Historical Data Trending

*   **User Value:** Provides crucial context by allowing users to see how a component has been performing over time, making it easier to diagnose intermittent faults or analyze long-term efficiency.
*   **Feature Description:** Add a "View History" button to each point in the Dashboard modal. Clicking it opens a new view within the modal displaying a line chart of the point's value over the last 24 hours.
*   **Technical Plan:**
    1.  Integrate a lightweight charting library like `recharts`.
    2.  Create a new `HistoricalChart` component.
    3.  When the `Dashboard` component mounts, it will generate or fetch mock historical data for all its points.
    4.  In the `Dashboard` modal, add a state variable to track which point's history is being viewed. When a user clicks the history button, this state is set, and the `HistoricalChart` component is rendered with the relevant data.

### Feature 3.2: Comparative Analysis View

*   **User Value:** Enables direct, side-by-side comparison of similar equipment, which is invaluable for performance benchmarking and identifying underperforming assets.
*   **Feature Description:** Allow the user to select multiple nodes in the diagram (e.g., by holding `Ctrl`+Click). When multiple nodes are selected, the side panel and dashboard adapt to show a comparative view of their key data points.
*   **Technical Plan:**
    1.  In `App.tsx`, change the `selectedNode` state to `selectedNodes` and store an array of `DiagramNodeData`.
    2.  Update the `DiagramWrapper`'s click handler to support multi-selection logic.
    3.  Refactor the `SidePanel` to display a summary when `selectedNodes.length > 1`.
    4.  Refactor the `Dashboard` component to render multiple columns or a comparison table when it receives an array of nodes, allowing for a side-by-side view of their live data.
