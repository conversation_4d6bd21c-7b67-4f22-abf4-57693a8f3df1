import { useState, useEffect } from 'react';

declare const go: any;

export const useGoJSLoader = (): boolean => {
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const checkGoJS = () => {
            if (typeof go !== 'undefined' && go.GraphObject) {
                setIsLoading(false);
            } else {
                setTimeout(checkGoJS, 100);
            }
        };
        const timeoutId = setTimeout(checkGoJS, 0);

        return () => clearTimeout(timeoutId);
    }, []);

    return isLoading;
};
