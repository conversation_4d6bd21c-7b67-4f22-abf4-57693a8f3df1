import { useState, useEffect, useRef } from 'react';
import { initializeChat, sendMessageToAi } from '../services/geminiService';
import type { ChatMessage, MasterTopology, ValidationReportData, ControlReadinessSummaryData, AiActionCommand, OntologyGraph } from '../types';
import type { Chat } from "@google/genai";

export const useChatbot = (
    topologyData: MasterTopology,
    reportData: ValidationReportData,
    summaryData: ControlReadinessSummaryData,
    ontologyData: OntologyGraph,
    executeAiAction: (command: AiActionCommand) => void
) => {
    const [isChatbotOpen, setIsChatbotOpen] = useState(false);
    const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
    const [isBotLoading, setIsBotLoading] = useState(false);
    const chatRef = useRef<Chat | null>(null);

    const resetChat = () => {
        chatRef.current = null;
        setChatMessages([]);
    }

    useEffect(() => {
        const initChat = async () => {
            if (isChatbotOpen && !chatRef.current) {
                try {
                    setIsBotLoading(true);
                    const newChat = initializeChat(topologyData, reportData, summaryData, ontologyData);
                    chatRef.current = newChat;
                    
                    const response = await sendMessageToAi(newChat, "Hello, introduce yourself and explain you can control the UI and trace paths.");
                    if (response.type === 'text') {
                        setChatMessages([{ role: 'model', content: response.content }]);
                    }
                } catch (error) {
                    console.error("Failed to initialize or communicate with Gemini AI:", error);
                    setChatMessages([{role: 'model', content: "Sorry, I couldn't connect to the AI service. Please check the API key configuration."}]);
                } finally {
                    setIsBotLoading(false);
                }
            }
        };
        initChat();
    }, [isChatbotOpen, topologyData, reportData, summaryData, ontologyData]);

    const handleSendMessage = async (message: string) => {
        if (!chatRef.current) return;

        setIsBotLoading(true);
        const userMessage: ChatMessage = { role: 'user', content: message };
        setChatMessages(prev => [...prev, userMessage]);

        try {
            const response = await sendMessageToAi(chatRef.current, message);
            
            if (response.type === 'action') {
                executeAiAction(response.command);
                const actionMessage = response.command.action === 'CALCULATE_DIJKSTRA_PATH'
                    ? `Ok, I've highlighted the shortest path from ${response.command.payload.startNodeKey} to ${response.command.payload.endNodeKey}.`
                    : `Ok, I've executed the action: ${response.command.action}.`;
                setChatMessages(prev => [...prev, { role: 'model', content: actionMessage }]);
            } else {
                setChatMessages(prev => [...prev, { role: 'model', content: response.content }]);
            }

        } catch (error) {
            console.error("Failed to send message:", error);
            setChatMessages(prev => [...prev, { role: 'model', content: "There was an error processing your request." }]);
        } finally {
            setIsBotLoading(false);
        }
    };

    return {
        isChatbotOpen,
        setIsChatbotOpen,
        chatMessages,
        isBotLoading,
        handleSendMessage,
        resetChat
    };
};