import type { OntologyGraph } from '../types';

export const ontologyGraph: OntologyGraph = {
  "nodes": [
    {
      "id": "Goal",
      "label": "HVAC Control Goal",
      "type": "Goal",
      "description": "Minimize energy consumption while maintaining occupant comfort within defined setpoints."
    },
    {
      "id": "System",
      "label": "HVAC System",
      "type": "Concept",
      "description": "The entire collection of equipment for heating, ventilation, and air conditioning."
    },
    {
      "id": "Plant",
      "label": "Central Plant",
      "type": "Concept",
      "description": "Primary sources of heating and cooling, like chillers and boilers."
    },
    {
      "id": "AirSide",
      "label": "Air-Side System",
      "type": "Concept",
      "description": "Equipment that moves and conditions air, like AHUs."
    },
    {
      "id": "Zone",
      "label": "Terminal Zone",
      "type": "Concept",
      "description": "The final delivery point of conditioned air to a space, managed by terminal units like VAVs."
    },
    {
      "id": "ControlPoint",
      "label": "Control Point",
      "type": "Concept",
      "description": "A sensor, setpoint, or actuator that provides data or accepts commands."
    },
    {
      "id": "AHU-3",
      "label": "AHU-3",
      "type": "Instance",
      "description": "An instance of an Air Handling Unit."
    },
    {
      "id": "VAV-08-1",
      "label": "VAV-08-1",
      "type": "Instance",
      "description": "An instance of a Variable Air Volume terminal unit."
    }
  ],
  "links": [
    { "source": "System", "target": "Goal", "label": "achieves" },
    { "source": "Plant", "target": "System", "label": "part_of" },
    { "source": "AirSide", "target": "System", "label": "part_of" },
    { "source": "Zone", "target": "System", "label": "part_of" },
    { "source": "ControlPoint", "target": "System", "label": "part_of" },
    { "source": "AHU-3", "target": "AirSide", "label": "is_a" },
    { "source": "VAV-08-1", "target": "Zone", "label": "is_a" }
  ]
};