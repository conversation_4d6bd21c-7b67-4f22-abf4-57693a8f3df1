import type { ControlReadinessSummaryData } from '../types';

export const controlReadinessSummary: ControlReadinessSummaryData = {
  "Summary_Timestamp": "2025-09-23T18:44:59Z",
  "Inventory_Summary": {
    "Total_Floors": 24,
    "Total_Zones": 1,
    "Total_Equipment_Assets": 333,
    "Total_Points": 54
  },
  "Asset_Distribution_By_Type": {
    "AHU": 2,
    "VAV": 208,
    "MSSB": 105,
    "FCU": 5,
    "PAC": 4,
    "PUMP": 6,
    "CHLR": 2,
    "CTWR": 2,
    "HX": 1,
    "OTHER": 0
  },
  "Point_Distribution_By_Control_Layer": {
    "Sensor": 22,
    "Actuator": 0,
    "Setpoint": 10,
    "Command": 16,
    "Status": 4,
    "Alarm": 2
  },
  "Control_Capability_Summary": {
    "Directly_Controllable_Zones_Temp": 0,
    "Directly_Controllable_Zones_Flow": 0,
    "Criticality_High_Zones_Count": 0,
    "Criticality_Medium_Zones_Count": 1,
    "Criticality_Low_Zones_Count": 0
  },
  "Data_Completeness": {
    "Assets_With_Points_Pct": 0.6,
    "Assets_With_Critical_Points_Pct": 0.6,
    "Message": "A very low percentage of assets have mapped points, limiting visibility and control readiness. The AHUs are well-defined, but terminal units lack data."
  }
};
