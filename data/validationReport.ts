import type { ValidationReportData } from '../types';

export const validationReport: ValidationReportData = {
  "Validation_Timestamp": "2025-09-23T18:44:59Z",
  "Schema_Compliance": {
    "Status": "Passed",
    "Details": "Master Topology JSON conforms to the canonical schema."
  },
  "Connectivity_Checks": {
    "Status": "Warning",
    "Issues": [
      {
        "Check": "VAV to AHU Connectivity",
        "Message": "No explicit VAV-to-AHU mapping was provided. Connectivity was inferred by grouping floors. VAVs on floors Ground-12 were assigned to AHU-3. VAVs on floors 13-23 were assigned to AHU-4. This requires verification.",
        "Count": 208
      },
      {
        "Check": "Plant Equipment Connectivity",
        "Message": "No explicit upstream/downstream data was provided for plant equipment. Connectivity could not be established.",
        "Count": 0
      }
    ]
  },
  "Point_Mapping_Checks": {
    "Status": "Warning",
    "Issues": [
      {
        "Check": "Assets Without Points",
        "Message": "Found 331 equipment assets with no points mapped. This is expected for assets like MSSB but indicates missing point data for VAVs, FCUs, etc.",
        "Count": 331,
        "Example_Assets": [
          "VAV-05-7",
          "MSSB-13-6",
          "VAV-16-3",
          "FCU-12-12",
          "PAC-11-11"
        ]
      },
      {
        "Check": "Canonical Point Name Inference",
        "Message": "Point names were canonicalized from multiple sources using heuristics (e.g., 'Return_Air_Temperature' -> 'Return_Air_Temp'). A formal ontology mapping is recommended for accuracy.",
        "Count": 54
      }
    ]
  },
  "Data_Source_Checks": {
    "Status": "Warning",
    "Issues": [
      {
        "Check": "Inconsistent Data Structures",
        "Message": "The three source files contain overlapping and sometimes conflicting flat-list data. A single source of truth for asset inventory and point lists is recommended.",
        "Sources": [
          "bms_data_mapping.json",
          "files_records.json",
          "ontology.json"
        ]
      },
      {
        "Check": "Missing Spatial Hierarchy",
        "Message": "Floor data was present, but specific Room and Zone locations for most equipment were absent. A spatial hierarchy was synthesized based on equipment names.",
        "Sources": [
          "files_records.json"
        ]
      }
    ]
  },
  "Summary": {
    "Status": "Passed with Warnings",
    "Total_Checks": 5,
    "Passed_Count": 1,
    "Warning_Count": 4,
    "Failed_Count": 0,
    "Overall_Message": "Topology generated successfully but relies on significant inference due to incomplete source data. Review inferred connectivity and missing points."
  }
};
