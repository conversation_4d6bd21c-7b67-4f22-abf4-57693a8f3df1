import type { MasterTopology } from '../types';

export const masterTopology: MasterTopology = {
  "Building": "BarTech_160_Ann_St",
  "Floors": [
    {
      "Floor": "Ground",
      "Rooms": [
        {
          "Room": "MSSB_Mechanical_Room",
          "Zones": [
            {
              "Zone_ID": "Ground-MechZone-AHU",
              "Zone_Type": "HVAC",
              "Criticality": "High",
              "Served_By": { "AHU": null, "Secondary": [] },
              "Equipment": [
                { "Equip_ID": "AHU-3", "Equip_Type": "AHU", "Points": [ { "Name": "Supply_Air_Temp_Setpoint" }, { "Name": "Chilled_Water_Valve_Demand" }, { "Name": "Cooling_Demand" }, { "Name": "Discharge_Air_Humidity" }, { "Name": "Discharge_Air_Temp" }, { "Name": "Dumpback_Damper_Demand" }, { "Name": "Economy_Available" }, { "Name": "Filter_Differential_Pressure" }, { "Name": "Max_Supply_Air_Temp_Setpoint" }, { "Name": "Min_Supply_Air_Temp_Setpoint" }, { "Name": "Outside_Damper_Demand" }, { "Name": "Relief_Damper_Demand" }, { "Name": "Return_Air_CO2_Sensor" }, { "Name": "Return_Air_CO2_Setpoint" }, { "Name": "Return_Air_Humidity" }, { "Name": "Return_Air_Temp" }, { "Name": "Return_Damper_Demand" }, { "Name": "Return_Fan_Fault" }, { "Name": "Return_Fan_Request" }, { "Name": "Return_Fan_Status" }, { "Name": "Return_Fan_VSD_Demand" }, { "Name": "Supply_Air_Humidity" }, { "Name": "Supply_Air_Temp" }, { "Name": "Supply_Fan_VSD_Demand" }, { "Name": "Supply_Fan_Static_Pressure" }, { "Name": "Supply_Fan_Static_Pressure_Setpoint" }, { "Name": "Supply_Fan_Fault" }, { "Name": "Supply_Fan_Request" }, { "Name": "Supply_Fan_Status" } ] },
                { "Equip_ID": "AHU-4", "Equip_Type": "AHU", "Points": [ { "Name": "Supply_Air_Temp_Setpoint" }, { "Name": "Chilled_Water_Valve_Demand" }, { "Name": "Cooling_Demand" }, { "Name": "Discharge_Air_Humidity" }, { "Name": "Discharge_Air_Temp" }, { "Name": "Dumpback_Damper_Demand" }, { "Name": "Economy_Available" }, { "Name": "Filter_Differential_Pressure" }, { "Name": "Max_Supply_Air_Temp_Setpoint" }, { "Name": "Min_Supply_Air_Temp_Setpoint" }, { "Name": "Outside_Damper_Demand" }, { "Name": "Relief_Damper_Demand" }, { "Name": "Return_Air_CO2_Sensor" }, { "Name": "Return_Air_CO2_Setpoint" }, { "Name": "Return_Air_Humidity" }, { "Name": "Return_Air_Temp" }, { "Name": "Return_Damper_Demand" }, { "Name": "Return_Fan_Fault" }, { "Name": "Return_Fan_Request" }, { "Name": "Return_Fan_Status" }, { "Name": "Return_Fan_VSD_Demand" }, { "Name": "Supply_Air_Humidity" }, { "Name": "Supply_Air_Temp" }, { "Name": "Supply_Fan_VSD_Demand" }, { "Name": "Supply_Fan_Static_Pressure" } ] }
              ]
            }
          ]
        }
      ]
    }
  ],
  "CrossRefs": {
     "AHU_to_VAVs": {
      "AHU-3": [ "VAV-01-10", "VAV-01-1", "VAV-04-7", "VAV-04-1", "VAV-05-7", "VAV-05-1", "VAV-06-7", "VAV-06-1", "VAV-07-3", "VAV-07-1", "VAV-08-4", "VAV-08-1", "VAV-09-1", "VAV-09-8", "VAV-10-9", "VAV-10-1", "VAV-11-1", "VAV-11-8", "VAV-12-3", "VAV-12-1" ],
      "AHU-4": [ "VAV-13-6", "VAV-13-1", "VAV-14-4", "VAV-14-1", "VAV-15-4", "VAV-15-1", "VAV-16-3", "VAV-16-1", "VAV-17-6", "VAV-17-1", "VAV-18-8", "VAV-18-1", "VAV-19-7", "VAV-19-1", "VAV-20-7", "VAV-20-1", "VAV-21-3", "VAV-21-1", "VAV-23-2", "VAV-23-1" ]
     }
  }
};