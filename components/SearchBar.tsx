import React, { useState, useMemo, useEffect, useRef } from 'react';
import type { DiagramNodeData } from '../types';

interface SearchBarProps {
    nodes: DiagramNodeData[];
    onSelect: (node: DiagramNodeData) => void;
}

export const SearchBar: React.FC<SearchBarProps> = ({ nodes, onSelect }) => {
    const [query, setQuery] = useState('');
    const [results, setResults] = useState<DiagramNodeData[]>([]);
    const [isOpen, setIsOpen] = useState(false);
    const searchRef = useRef<HTMLDivElement>(null);

    const allSearchableNodes = useMemo(() => 
        nodes.filter(n => n.category !== 'Point'), 
    [nodes]);

    useEffect(() => {
        if (query.length > 1) {
            const lowerCaseQuery = query.toLowerCase();
            const filtered = allSearchableNodes.filter(node => 
                node.name.toLowerCase().includes(lowerCaseQuery)
            );
            setResults(filtered.slice(0, 10)); // Limit results
            setIsOpen(true);
        } else {
            setResults([]);
            setIsOpen(false);
        }
    }, [query, allSearchableNodes]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleSelect = (node: DiagramNodeData) => {
        onSelect(node);
        setQuery('');
        setIsOpen(false);
    };

    return (
        <div ref={searchRef} className="relative w-full max-w-md">
            <div className="relative">
                <input
                    type="text"
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    placeholder="Search for components (e.g., AHU-3, VAV-01-10)..."
                    className="w-full bg-gray-800 border border-gray-600 text-white rounded-lg py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                    </svg>
                </div>
            </div>
            {isOpen && results.length > 0 && (
                <ul className="absolute z-10 mt-1 w-full bg-gray-800 border border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {results.map(node => (
                        <li 
                            key={node.key}
                            onClick={() => handleSelect(node)}
                            className="px-4 py-2 text-white hover:bg-gray-700 cursor-pointer flex justify-between items-center"
                        >
                            <span>{node.name}</span>
                            <span className="text-xs bg-gray-600 text-gray-300 px-2 py-0.5 rounded-full">{node.category}</span>
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
};
