// FIX: Add declaration for go namespace
declare const go: any;

import React, { useRef, useEffect } from 'react';
import type { DiagramNodeData, DiagramLinkData, DijkstraPath } from '../types';

interface DiagramProps {
    nodeDataArray: DiagramNodeData[];
    linkDataArray: DiagramLinkData[];
    // FIX: Changed go.ObjectData to any to resolve namespace error.
    onNodeSelection: (nodeData: any | null) => void;
    criticalityFilter: string;
    highlightedPath: DijkstraPath | null;
}

// Inlined FanIcon as a data URI to remove ReactDOMServer dependency
const fanIconSvgString = `<svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#ffffff" strokeWidth="1.5"><g id="SVGRepo_bgCarrier" strokeWidth="0"></g><g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2Z" strokeOpacity="0.5"></path><path d="M12 5V2.5" strokeLinecap="round"></path><path d="M12 21.5V19" strokeLinecap="round"></path><path d="M19 12L21.5 12" strokeLinecap="round"></path><path d="M2.5 12L5 12" strokeLinecap="round"></path><path d="M17.6569 6.34315L19.435 4.56502" strokeLinecap="round"></path><path d="M4.56502 19.435L6.34315 17.6569" strokeLinecap="round"></path><path d="M17.6569 17.6569L19.435 19.435" strokeLinecap="round"></path><path d="M4.56502 4.56502L6.34315 6.34315" strokeLinecap="round"></path><path d="M12 12C9.23858 12 7 9.76142 7 7C7 4.23858 9.23858 2 12 2C14.7614 2 17 4.23858 17 7C17 9.76142 14.7614 12 12 12Z" transform="rotate(45 12 12)" strokeOpacity="0.5"></path><path d="M12 12C9.23858 12 7 9.76142 7 7C7 4.23858 9.23858 2 12 2C14.7614 2 17 4.23858 17 7C17 9.76142 14.7614 12 12 12Z" transform="rotate(-45 12 12)"></path></g></svg>`;
const fanIconDataUri = "data:image/svg+xml;charset=utf-8," + encodeURIComponent(fanIconSvgString);

export const DiagramWrapper = React.forwardRef<any, DiagramProps>(({ nodeDataArray, linkDataArray, onNodeSelection, criticalityFilter, highlightedPath }, ref) => {
    const diagramRef = useRef<HTMLDivElement>(null);
    const diagramInstance = useRef<any | null>(null);
    const animationRef = useRef<any | null>(null); // Ref to store the GoJS Animation instance

    React.useImperativeHandle(ref, () => diagramInstance.current);

    useEffect(() => {
        if (!diagramRef.current || typeof go === 'undefined') return;

        const $ = go.GraphObject.make;

        const createSelectionAdornment = (shape: string) =>
            $(go.Adornment, "Auto",
                $(go.Shape, shape, {
                    fill: null,
                    stroke: "#06b6d4",
                    strokeWidth: 3,
                    strokeJoin: "round",
                    shadowColor: "#06b6d4",
                    shadowBlur: 12
                }),
                $(go.Placeholder)
            );

        const selectionAdornmentRoundedRect = createSelectionAdornment("RoundedRectangle");
        const selectionAdornmentEllipse = createSelectionAdornment("Ellipse");
        const selectionAdornmentCircle = createSelectionAdornment("Circle");

        const myDiagram = $(go.Diagram, diagramRef.current, {
            "undoManager.isEnabled": true,
            layout: $(go.TreeLayout, {
                angle: 90,
                layerSpacing: 70,
                nodeSpacing: 30,
                alternateAngle: 90,
                alternateLayerSpacing: 70,
                alternateNodeSpacing: 30,
            }),
            "InitialContentAlignment": go.Spot.Center,
            "animationManager.isEnabled": true,
            "animationManager.duration": 400,
        });

        const highlightNode = (e: any, obj: any, show: boolean) => {
            if (show) {
                const shape = obj.findObject("SHAPE");
                if (shape) {
                    shape.stroke = "#38bdf8";
                    shape.strokeWidth = (shape as any).originalStrokeWidth ? (shape as any).originalStrokeWidth + 2 : 4;
                }
            } else {
                const shape = obj.findObject("SHAPE");
                if (shape) {
                    shape.stroke = (shape as any).originalStroke;
                    shape.strokeWidth = (shape as any).originalStrokeWidth;
                }
            }
        };

        const addHoverEffect = (node: any) => {
            node.mouseEnter = (e: any, obj: any) => highlightNode(e, obj, true);
            node.mouseLeave = (e: any, obj: any) => highlightNode(e, obj, false);
            const shape = node.findObject("SHAPE");
            if (shape) {
                (shape as any).originalStroke = shape.stroke;
                (shape as any).originalStrokeWidth = shape.strokeWidth;
            }
            return node;
        };
        
        const textBlock = (color?: string, font?: string) => $(go.TextBlock, 
            { margin: 10, stroke: color || "#e5e7eb", font: font || "14px Inter", shadowColor: "#000", shadowBlur: 5, shadowOffsetX:1, shadowOffsetY:1 },
            new go.Binding("text", "name"));

        const baseNodeStyle = () => [
          new go.Binding("location", "loc", go.Point.parse).makeTwoWay(go.Point.stringify),
          { locationSpot: go.Spot.Center, opacity: 1, transitions: $(go.AnimationTrigger, "opacity", { duration: 300 }) }
        ];

        myDiagram.nodeTemplateMap.add("Building", addHoverEffect(
            $(go.Node, "Auto", ...baseNodeStyle(),
                { selectionAdornmentTemplate: selectionAdornmentRoundedRect },
                $(go.Shape, "RoundedRectangle", { name: "SHAPE", fill: "#1e3a8a", stroke: "#93c5fd", strokeWidth: 3, shadowColor: "#000", shadowBlur: 10, shadowOffsetX: 0, shadowOffsetY: 4 }),
                textBlock("#ffffff", "bold 18px Inter")
            )));

        myDiagram.nodeTemplateMap.add("Floor", addHoverEffect(
            $(go.Node, "Auto", ...baseNodeStyle(),
                { selectionAdornmentTemplate: selectionAdornmentRoundedRect },
                $(go.Shape, "RoundedRectangle", { name: "SHAPE", fill: "#1d4ed8", stroke: "#60a5fa", strokeWidth: 2 }),
                textBlock("#ffffff", "bold 16px Inter")
            )));
        
        myDiagram.nodeTemplateMap.add("Room", addHoverEffect(
            $(go.Node, "Auto", ...baseNodeStyle(),
                { selectionAdornmentTemplate: selectionAdornmentRoundedRect },
                $(go.Shape, "RoundedRectangle", { name: "SHAPE", fill: "#374151", stroke: "#6b7280", strokeWidth: 1, strokeDashArray: [5, 5] }),
                textBlock()
            )));
            
        myDiagram.nodeTemplateMap.add("Zone", addHoverEffect(
            $(go.Node, "Auto", ...baseNodeStyle(),
                { selectionAdornmentTemplate: selectionAdornmentEllipse },
                $(go.Shape, "Ellipse", 
                    { name: "SHAPE" },
                    new go.Binding("fill", "data", (d: any) => {
                       switch(d.Criticality) {
                           case "High": return "#ef4444";
                           case "Medium": return "#f97316";
                           default: return "#22c55e";
                       }
                    }),
                    new go.Binding("stroke", "data", (d: any) => d.Criticality === "High" ? "#fca5a5" : "#e5e7eb"),
                    new go.Binding("strokeWidth", "data", (d: any) => d.Criticality === "High" ? 3 : 2),
                    new go.Binding("shadowColor", "data", (d: any) => d.Criticality === "High" ? "#ef4444" : null),
                    new go.Binding("shadowBlur", "data", (d: any) => d.Criticality === "High" ? 15 : 0)
                ),
                textBlock()
            )));

        const equipmentShape = (fill: string) => $(go.Shape, "Rectangle", { name: "SHAPE", fill: fill, stroke: null, width: 90, height: 45, corner: 8 });

        myDiagram.nodeTemplateMap.add("AHU", addHoverEffect(
             $(go.Node, "Vertical", ...baseNodeStyle(),
                { selectionAdornmentTemplate: selectionAdornmentRoundedRect },
                $(go.Panel, "Auto",
                    equipmentShape("#9a3412"),
                    $(go.Picture, { source: fanIconDataUri, width: 32, height: 32 })
                ),
                textBlock("#f3f4f6", "12px Inter")
            )));
        
        myDiagram.nodeTemplateMap.add("VAV", addHoverEffect(
             $(go.Node, "Vertical", ...baseNodeStyle(),
                { selectionAdornmentTemplate: selectionAdornmentRoundedRect },
                equipmentShape("#ca8a04"),
                textBlock("#f3f4f6", "12px Inter")
            )));

         myDiagram.nodeTemplateMap.add("Point", addHoverEffect(
            $(go.Node, "Auto", ...baseNodeStyle(),
                { selectionAdornmentTemplate: selectionAdornmentCircle },
                $(go.Shape, "Circle", { name: "SHAPE", width: 70, height: 70, stroke: "#6b7280" }),
                new go.Binding("fill", "data", (d: any) => {
                    const layer = d.Control_Layer || "Sensor";
                    switch(layer) {
                        case "Sensor": return "#3b82f6";
                        case "Setpoint": return "#22c55e";
                        case "Command": return "#f97316";
                        case "Alarm": return "#ef4444";
                        case "Status": return "#6b7280";
                        default: return "#4b5563";
                    }
                }),
                $(go.TextBlock, { font: "10px Inter", stroke: "white", textAlign: "center", maxSize: new go.Size(65, NaN), wrap: go.TextBlock.WrapFit },
                    new go.Binding("text", "name"))
            )));

        const baseLinkStyle = () => [
             { routing: go.Link.Orthogonal, corner: 10, opacity: 1, transitions: $(go.AnimationTrigger, "opacity", { duration: 300 }) },
             $(go.Shape, { name: "LINK_SHAPE", strokeWidth: 2, stroke: "#6b7280" }),
             $(go.Shape, { name: "ARROW_SHAPE", toArrow: "Triangle", fill: "#6b7280", stroke: null })
        ];

        myDiagram.linkTemplate = $(go.Link, ...baseLinkStyle());

        myDiagram.linkTemplateMap.add("DownstreamLink",
            $(go.Link, { routing: go.Link.AvoidsNodes, curve: go.Link.JumpOver, opacity: 1, transitions: $(go.AnimationTrigger, "opacity", { duration: 300 }) },
                $(go.Shape, { name: "LINK_SHAPE", strokeWidth: 2, stroke: "#3b82f6", strokeDashArray: [6, 6] }),
                $(go.Shape, { name: "ARROW_SHAPE", toArrow: "Triangle", fill: "#3b82f6", stroke: null, scale: 0.8 })
            ));
            
        myDiagram.addDiagramListener("ObjectSingleClicked", (e: any) => {
            const part = e.subject.part;
            if (part instanceof go.Node) {
                onNodeSelection(part.data);
            }
        });

        myDiagram.addDiagramListener("BackgroundSingleClicked", () => {
             onNodeSelection(null);
        });

        diagramInstance.current = myDiagram;

        return () => {
            if (diagramInstance.current) {
                diagramInstance.current.div = null;
            }
        };
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        if (diagramInstance.current) {
            diagramInstance.current.model = new go.GraphLinksModel(nodeDataArray, linkDataArray);
        }
    }, [nodeDataArray, linkDataArray]);

    useEffect(() => {
        const diagram = diagramInstance.current;
        if (!diagram) return;

        diagram.commit((d: any) => {
            if (criticalityFilter === 'All') {
                d.nodes.each((node: any) => node.opacity = 1);
                d.links.each((link: any) => link.opacity = 1);
                return;
            }

            const highlightedKeys = new Set();
            d.nodes.each((node: any) => {
                if (node.data.category === 'Zone' && node.data.data.Criticality === criticalityFilter) {
                    highlightedKeys.add(node.data.key);
                    const walkTree = (n: any) => {
                        if (!n) return;
                        highlightedKeys.add(n.data.key);
                        n.findNodesInto().each(walkTree);
                        n.findNodesOutOf().each(walkTree);
                    };
                    walkTree(node);
                }
            });

            d.nodes.each((node: any) => {
                node.opacity = highlightedKeys.has(node.data.key) ? 1 : 0.2;
            });
            d.links.each((link: any) => {
                link.opacity = (highlightedKeys.has(link.fromNode?.data.key) && highlightedKeys.has(link.toNode?.data.key)) ? 1 : 0.1;
            });

        }, 'update criticality highlights');

    }, [criticalityFilter]);
    
    useEffect(() => {
        const diagram = diagramInstance.current;
        if (!diagram) return;

        // Stop any previous animation
        if (animationRef.current) {
            animationRef.current.stop();
            animationRef.current = null;
        }
        
        diagram.commit((d: any) => {
            // Reset all styles first
            d.nodes.each((node: any) => {
                node.opacity = 1;
                const shape = node.findObject("SHAPE");
                if (shape) {
                    node.path.shadowColor = node.data.data?.Criticality === 'High' ? "#ef4444" : null;
                    node.path.shadowBlur = node.data.data?.Criticality === 'High' ? 15 : 0;
                }
            });
            d.links.each((link: any) => {
                link.opacity = 1;
                const linkShape = link.findObject("LINK_SHAPE");
                const arrowShape = link.findObject("ARROW_SHAPE");
                if (linkShape) {
                    linkShape.stroke = link.data.category === 'DownstreamLink' ? '#3b82f6' : '#6b7280';
                    linkShape.strokeWidth = 2;
                }
                if (arrowShape) {
                    arrowShape.fill = link.data.category === 'DownstreamLink' ? '#3b82f6' : '#6b7280';
                }
            });

            if (!highlightedPath) return;

            const nodeKeys = new Set(highlightedPath.nodes);
            const linkSet = new Set(highlightedPath.links.map(l => `${l.from}|${l.to}`));

            // Dim all parts not in the path
            d.nodes.each((node: any) => {
                if (!nodeKeys.has(node.data.key)) {
                    node.opacity = 0.2;
                }
            });
            d.links.each((link: any) => {
                const key1 = `${link.fromNode?.data.key}|${link.toNode?.data.key}`;
                const key2 = `${link.toNode?.data.key}|${link.fromNode?.data.key}`;
                if (!linkSet.has(key1) && !linkSet.has(key2)) {
                    link.opacity = 0.1;
                }
            });
            
            // Animate the highlighted path
            const animation = new go.Animation();
            animation.duration = 1000;
            animation.easing = go.Animation.EaseLinear;
            animation.runCount = Infinity;

            highlightedPath.nodes.forEach(key => {
                const node = d.findNodeForKey(key);
                if (node) animation.add(node.path, "shadowBlur", 10, 25);
            });

            highlightedPath.links.forEach(linkData => {
                d.links.each((link: any) => {
                    if ((link.fromNode?.data.key === linkData.from && link.toNode?.data.key === linkData.to) ||
                        (link.fromNode?.data.key === linkData.to && link.toNode?.data.key === linkData.from))
                    {
                        const linkShape = link.findObject("LINK_SHAPE");
                        const arrowShape = link.findObject("ARROW_SHAPE");
                        if (linkShape) {
                            linkShape.stroke = "#facc15"; // Yellow
                            linkShape.strokeWidth = 4;
                            animation.add(linkShape, "strokeDashOffset", 10, 0);
                        }
                         if (arrowShape) arrowShape.fill = "#facc15";
                    }
                });
            });

            animation.start();
            animationRef.current = animation;
        }, 'highlight Dijkstra path');
    }, [highlightedPath]);


    return <div ref={diagramRef} className="gojs-container"></div>;
});
