import React, { useState, useEffect, useRef } from 'react';
import type { ChatMessage } from '../types';

interface ChatbotProps {
    isOpen: boolean;
    onClose: () => void;
    messages: ChatMessage[];
    onSendMessage: (message: string) => void;
    isLoading: boolean;
}

const TypingIndicator: React.FC = () => (
    <div className="flex items-center space-x-1.5 p-2">
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-[bounce_1s_infinite_0.1s]"></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-[bounce_1s_infinite_0.2s]"></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-[bounce_1s_infinite_0.3s]"></div>
    </div>
);

const SimpleMarkdown: React.FC<{ text: string }> = ({ text }) => {
    const lines = text.split('\n');
    return (
        <>
            {lines.map((line, i) => {
                if (line.startsWith('* ')) {
                    return <li key={i} className="ml-5">{line.substring(2)}</li>;
                }
                const parts = line.split(/(\*\*.*?\*\*)/g);
                return (
                    <p key={i} className="my-1">
                        {parts.map((part, j) => 
                            part.startsWith('**') && part.endsWith('**') ? 
                            <strong key={j}>{part.slice(2, -2)}</strong> : 
                            part
                        )}
                    </p>
                );
            })}
        </>
    );
};

const ChatMessageItem: React.FC<{ msg: ChatMessage }> = ({ msg }) => (
    <div className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'} animate-[messageFadeIn_0.5s_ease-in-out]`}>
        <div className={`max-w-xs md:max-w-sm rounded-lg px-4 py-2 shadow-md ${msg.role === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-200'}`}>
            <div className="prose prose-sm prose-invert max-w-none">
                 <SimpleMarkdown text={msg.content} />
            </div>
        </div>
    </div>
);

const InstantAIButtons: React.FC<{ onSendMessage: (prompt: string) => void }> = ({ onSendMessage }) => {
    const prompts = [
        {
            title: "Analyze System Goal",
            description: "Use the Ontology to find the system's goal & highlight critical assets.",
            prompt: "Based on the ontology, what is the primary goal of this HVAC system, and which components are most critical to achieving it? Highlight them."
        },
        {
            title: "Trace Fault Path",
            description: "Simulate a fault and use Dijkstra's algorithm to trace the signal path.",
            prompt: "Simulate a fault at VAV-08-1. Trace the shortest path from VAV-08-1 back to the building to identify the signal chain."
        },
        {
            title: "Suggest Optimization",
            description: "Recommend and apply an energy-saving strategy to a primary AHU.",
            prompt: "Suggest and apply an 'EnergySaving' control strategy for the primary air handling unit, AHU-3."
        },
        {
            title: "Identify Data Gaps",
            description: "Find the biggest data gap from the reports and explain its impact on the goal.",
            prompt: "Synthesizing the validation report and the ontology, what is the most significant data gap in this system and how does it impact the system's primary goal?"
        }
    ];

    return (
        <div className="grid grid-cols-2 gap-2 mb-4 animate-[messageFadeIn_0.7s_ease-in-out]">
            {prompts.map(p => (
                <button
                    key={p.title}
                    onClick={() => onSendMessage(p.prompt)}
                    className="bg-gray-700/50 hover:bg-gray-600/80 p-3 rounded-lg text-left transition-all duration-200 border border-gray-600 hover:border-blue-500"
                >
                    <p className="font-semibold text-sm text-blue-300">{p.title}</p>
                    <p className="text-xs text-gray-400 mt-1">{p.description}</p>
                </button>
            ))}
        </div>
    );
};


export const Chatbot: React.FC<ChatbotProps> = ({ isOpen, onClose, messages, onSendMessage, isLoading }) => {
    const [input, setInput] = useState('');
    const messagesEndRef = useRef<HTMLDivElement>(null);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(scrollToBottom, [messages, isLoading]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (input.trim() && !isLoading) {
            onSendMessage(input.trim());
            setInput('');
        }
    };

    return (
        <div className={`fixed bottom-28 right-6 w-full max-w-md h-[70vh] max-h-[600px] z-40 transition-all duration-300 ease-in-out ${isOpen ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4 pointer-events-none'}`}>
            <style>{`
                @keyframes bounce {
                    0%, 100% { transform: translateY(0); }
                    50% { transform: translateY(-6px); }
                }
                @keyframes messageFadeIn {
                    from { opacity: 0; transform: translateY(10px); }
                    to { opacity: 1; transform: translateY(0); }
                }
            `}</style>
            <div className="bg-gray-900/80 backdrop-blur-md border border-gray-700 rounded-xl h-full flex flex-col shadow-2xl">
                <header className="flex justify-between items-center p-4 border-b border-gray-700 shrink-0">
                    <h2 className="text-lg font-bold text-white flex items-center space-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                          <path strokeLinecap="round" strokeLinejoin="round" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                          <path strokeLinecap="round" strokeLinejoin="round" d="M17 9l-5 5-5-5" />
                        </svg>
                        <span>HVAC AI Assistant</span>
                    </h2>
                    <button onClick={onClose} className="text-gray-400 hover:text-white text-3xl leading-none">&times;</button>
                </header>
                <main className="flex-grow p-4 overflow-y-auto space-y-4">
                    {messages.map((msg, index) => (
                        <ChatMessageItem key={index} msg={msg} />
                    ))}
                    {messages.length <= 1 && !isLoading && (
                        <InstantAIButtons onSendMessage={onSendMessage} />
                    )}
                    {isLoading && (
                         <div className="flex justify-start">
                             <div className="bg-gray-700 rounded-lg px-4 py-2">
                                <TypingIndicator />
                             </div>
                         </div>
                    )}
                    <div ref={messagesEndRef} />
                </main>
                <footer className="p-4 border-t border-gray-700 shrink-0">
                    <form onSubmit={handleSubmit} className="flex items-center space-x-2">
                        <input
                            type="text"
                            value={input}
                            onChange={(e) => setInput(e.target.value)}
                            placeholder="Ask about AHU-3..."
                            disabled={isLoading}
                            className="w-full bg-gray-800 border border-gray-600 text-white rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all disabled:opacity-50"
                        />
                        <button type="submit" disabled={isLoading} className="bg-blue-600 hover:bg-blue-700 text-white rounded-lg p-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                             <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                <path strokeLinecap="round" strokeLinejoin="round" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                            </svg>
                        </button>
                    </form>
                </footer>
            </div>
        </div>
    );
};