import React from 'react';
import { DataUploader } from './DataUploader';
import { ValidationReport } from './ValidationReport';
import { ControlReadinessSummary } from './ControlReadinessSummary';
import { Dashboard } from './Dashboard';
import type { MasterTopology, ValidationReportData, ControlReadinessSummaryData, DiagramNodeData } from '../types';

interface AppModalsProps {
    isUploaderOpen: boolean;
    onCloseUploader: () => void;
    onDataUpload: (data: { topology: MasterTopology; report: ValidationReportData; summary: ControlReadinessSummaryData }) => void;
    
    isReportOpen: boolean;
    onCloseReport: () => void;
    reportData: ValidationReportData;

    isSummaryOpen: boolean;
    onCloseSummary: () => void;
    summaryData: ControlReadinessSummaryData;

    isDashboardOpen: boolean;
    onCloseDashboard: () => void;
    selectedNode: DiagramNodeData | null;
}

export const AppModals: React.FC<AppModalsProps> = ({
    isUploaderOpen,
    onCloseUploader,
    onDataUpload,
    isReportOpen,
    onCloseReport,
    reportData,
    isSummaryOpen,
    onCloseSummary,
    summaryData,
    isDashboardOpen,
    onCloseDashboard,
    selectedNode
}) => {
    return (
        <>
            {isUploaderOpen && <DataUploader onDataLoaded={onDataUpload} onClose={onCloseUploader} />}
            {isReportOpen && <ValidationReport report={reportData} onClose={onCloseReport} />}
            {isSummaryOpen && <ControlReadinessSummary summary={summaryData} onClose={onCloseSummary} />}
            {isDashboardOpen && selectedNode && <Dashboard nodeData={selectedNode} onClose={onCloseDashboard} />}
        </>
    );
};