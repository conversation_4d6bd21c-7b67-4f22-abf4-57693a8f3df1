import React, { useState, useCallback } from 'react';
import type { MasterTopology, ValidationReportData, ControlReadinessSummaryData, OntologyGraph } from '../types';
import { validateFileWithAi } from '../services/geminiService';

interface DataUploaderProps {
    onClose: () => void;
    onDataLoaded: (data: {
        topology: MasterTopology;
        report: ValidationReportData;
        summary: ControlReadinessSummaryData;
        ontology: OntologyGraph;
    }) => void;
}

type FileType = 'topology' | 'report' | 'summary' | 'ontology';
type ValidationStatus = 'idle' | 'parsing' | 'validating' | 'valid' | 'invalid';

interface FileInputAreaProps {
    label: string;
    fileType: FileType;
    onFileParsed: (type: FileType, data: any | null, isValid: boolean) => void;
}

const FileInputArea: React.FC<FileInputAreaProps> = ({ label, fileType, onFileParsed }) => {
    const [isDragOver, setIsDragOver] = useState(false);
    const [fileName, setFileName] = useState<string | null>(null);
    const [status, setStatus] = useState<ValidationStatus>('idle');
    const [message, setMessage] = useState<string | null>('JSON up to 10MB');

    const handleFile = async (file: File) => {
        setFileName(file.name);
        setMessage(null);
        setStatus('parsing');

        const reader = new FileReader();
        reader.readAsText(file);
        
        reader.onload = async (e) => {
            const content = e.target?.result as string;
            try {
                // Quick client-side parse check
                JSON.parse(content); 
                setStatus('validating');
                setMessage('Analyzing file structure with AI...');

                // Deeper validation with AI
                const validationResult = await validateFileWithAi(content, fileType);
                setMessage(validationResult.message);
                
                if (validationResult.isValid) {
                    setStatus('valid');
                    onFileParsed(fileType, JSON.parse(content), true);
                } else {
                    setStatus('invalid');
                    onFileParsed(fileType, null, false);
                }

            } catch (err) {
                setStatus('invalid');
                const errorMessage = err instanceof Error ? err.message : 'Invalid JSON format.';
                setMessage(errorMessage);
                onFileParsed(fileType, null, false);
            }
        };

        reader.onerror = () => {
            setStatus('invalid');
            setMessage('Failed to read the file.');
            onFileParsed(fileType, null, false);
        };
    };

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => { e.preventDefault(); e.stopPropagation(); setIsDragOver(true); };
    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => { e.preventDefault(); e.stopPropagation(); setIsDragOver(false); };
    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragOver(false);
        if (e.dataTransfer.files && e.dataTransfer.files[0]) handleFile(e.dataTransfer.files[0]);
    };
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) handleFile(e.target.files[0]);
    };
    
    const borderClasses = isDragOver ? 'border-blue-500 ring-2 ring-blue-500/50' :
                        status === 'valid' ? 'border-green-500' :
                        status === 'invalid' ? 'border-red-500' :
                        'border-gray-600';

    return (
        <div className="mb-4">
            <label className="block text-sm font-medium text-gray-300 mb-1 flex justify-between items-center">
                <span>{label}</span>
                 {status === 'valid' && <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" /></svg>}
                {status === 'invalid' && <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" /></svg>}
                {(status === 'parsing' || status === 'validating') && <div className="w-4 h-4 border-2 border-t-blue-400 border-gray-600 rounded-full animate-spin"></div>}
            </label>
            <div 
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                className={`relative mt-1 flex flex-col justify-center items-center px-6 pt-5 pb-6 border-2 border-dashed rounded-md transition-all duration-200 ${borderClasses}`}
            >
                <svg className="mx-auto h-12 w-12 text-gray-500" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true"><path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></svg>
                <div className="flex text-sm text-gray-400 mt-2">
                     <label htmlFor={fileType} className="relative cursor-pointer bg-gray-900 rounded-md font-medium text-blue-400 hover:text-blue-500 focus-within:outline-none">
                        <span>Upload a file</span>
                        <input id={fileType} name={fileType} type="file" className="sr-only" accept=".json" onChange={handleFileChange} />
                    </label>
                    <p className="pl-1">or drag and drop</p>
                </div>
                {fileName && <p className="text-xs text-gray-500 truncate max-w-xs mt-1">{fileName}</p>}
                {message && (
                    <p className={`text-xs mt-2 text-center ${status === 'invalid' ? 'text-red-400' : 'text-gray-500'}`}>
                        {message}
                    </p>
                )}
            </div>
        </div>
    );
};

export const DataUploader: React.FC<DataUploaderProps> = ({ onClose, onDataLoaded }) => {
    const [files, setFiles] = useState<Record<FileType, any | null>>({ topology: null, report: null, summary: null, ontology: null });
    const [validity, setValidity] = useState<Record<FileType, boolean>>({ topology: false, report: false, summary: false, ontology: false });

    const handleFileParsed = useCallback((type: FileType, data: any | null, isValid: boolean) => {
        setFiles(prev => ({...prev, [type]: data}));
        setValidity(prev => ({...prev, [type]: isValid}));
    }, []);

    const handleLoadData = () => {
        if (validity.topology && validity.report && validity.summary && validity.ontology) {
            onDataLoaded({
                topology: files.topology,
                report: files.report,
                summary: files.summary,
                ontology: files.ontology,
            });
        }
    };
    
    const allFilesValid = validity.topology && validity.report && validity.summary && validity.ontology;

    return (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-[fadeIn_0.3s_ease-out]">
            <div className="bg-gray-900 border border-gray-700 rounded-xl w-full max-w-lg flex flex-col shadow-2xl">
                <header className="flex justify-between items-center p-4 border-b border-gray-700">
                    <h2 className="text-xl font-bold text-white">Load System Data</h2>
                    <button onClick={onClose} className="text-gray-400 hover:text-white text-3xl leading-none">&times;</button>
                </header>
                <main className="p-6 overflow-y-auto">
                    <FileInputArea label="Master Topology JSON" fileType="topology" onFileParsed={handleFileParsed} />
                    <FileInputArea label="Validation Report JSON" fileType="report" onFileParsed={handleFileParsed} />
                    <FileInputArea label="Control Readiness JSON" fileType="summary" onFileParsed={handleFileParsed} />
                    <FileInputArea label="Ontology Graph JSON" fileType="ontology" onFileParsed={handleFileParsed} />
                </main>
                <footer className="p-4 border-t border-gray-700 flex justify-end space-x-2">
                    <button onClick={onClose} className="bg-gray-700 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors">Cancel</button>
                    <button 
                        onClick={handleLoadData} 
                        className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed" 
                        disabled={!allFilesValid}
                    >
                        Load Data
                    </button>
                </footer>
            </div>
        </div>
    );
};