import React from 'react';
import type { ControlReadinessSummaryData, AssetDistribution, PointDistribution } from '../types';

interface SummaryProps {
    summary: ControlReadinessSummaryData;
    onClose: () => void;
}

// FIX: Changed JSX.Element to React.ReactNode to resolve namespace error.
const KPICard: React.FC<{ title: string; value: number | string; icon: React.ReactNode }> = ({ title, value, icon }) => (
    <div className="bg-gray-800/60 p-4 rounded-lg flex items-center space-x-4 border border-gray-700">
        <div className="text-blue-400">{icon}</div>
        <div>
            <p className="text-gray-400 text-sm">{title}</p>
            <p className="text-2xl font-bold text-white">{value}</p>
        </div>
    </div>
);

const BarChart: React.FC<{ title: string; data: AssetDistribution | PointDistribution }> = ({ title, data }) => {
    // FIX: Assert the type of Object.entries to resolve downstream type errors for values.
    const sortedData = (Object.entries(data) as [string, number][]).filter(([, value]) => value > 0).sort((a, b) => b[1] - a[1]);
    const max = Math.max(...sortedData.map(([, value]) => value));
    
    const colors = ['bg-cyan-500', 'bg-blue-500', 'bg-indigo-500', 'bg-purple-500', 'bg-pink-500', 'bg-teal-500'];

    return (
        <div className="bg-gray-800/60 p-4 rounded-lg border border-gray-700">
            <h3 className="text-lg font-bold text-blue-300 mb-4">{title}</h3>
            <div className="space-y-3">
                {sortedData.map(([key, value], index) => (
                    <div key={key} className="grid grid-cols-4 items-center gap-2 text-sm">
                        <span className="text-gray-300 col-span-1 truncate">{key}</span>
                        <div className="col-span-3 flex items-center">
                            <div className="w-full bg-gray-700 rounded-full h-4 mr-2">
                                <div
                                    className={`${colors[index % colors.length]} h-4 rounded-full transition-all duration-1000 ease-out`}
                                    style={{ width: `${(value / max) * 100}%` }}
                                ></div>
                            </div>
                            <span className="font-semibold text-white w-10 text-right">{value}</span>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export const ControlReadinessSummary: React.FC<SummaryProps> = ({ summary, onClose }) => {
    const { Inventory_Summary, Asset_Distribution_By_Type, Point_Distribution_By_Control_Layer, Data_Completeness } = summary;
    
    return (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-[fadeIn_0.3s_ease-out]">
            <div className="bg-gray-900 border border-gray-700 rounded-xl w-full max-w-4xl h-full max-h-[90vh] flex flex-col shadow-2xl">
                <header className="flex justify-between items-center p-4 border-b border-gray-700 sticky top-0 bg-gray-900/80 backdrop-blur-sm">
                    <h2 className="text-xl font-bold text-white">Control Readiness Summary</h2>
                    <button onClick={onClose} className="text-gray-400 hover:text-white text-3xl leading-none transition-colors">&times;</button>
                </header>
                <main className="p-6 overflow-y-auto space-y-6">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <KPICard title="Total Floors" value={Inventory_Summary.Total_Floors} icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m-1 4h1m5-4h1m-1 4h1m-1-4h1m-1 4h1" /></svg>} />
                        <KPICard title="Equipment Assets" value={Inventory_Summary.Total_Equipment_Assets} icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" /></svg>} />
                        <KPICard title="Total Zones" value={Inventory_Summary.Total_Zones} icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" /></svg>} />
                        <KPICard title="Total Points" value={Inventory_Summary.Total_Points} icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" /></svg>} />
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <BarChart title="Asset Distribution by Type" data={Asset_Distribution_By_Type} />
                        <BarChart title="Point Distribution by Control Layer" data={Point_Distribution_By_Control_Layer} />
                    </div>
                     
                    <div className="bg-gray-800/60 p-4 rounded-lg border border-gray-700">
                         <h3 className="text-lg font-bold text-blue-300 mb-2">Data Completeness</h3>
                         <p className="text-sm text-gray-400 mb-4">{Data_Completeness.Message}</p>
                         <div className="space-y-3">
                            <div>
                                <div className="flex justify-between mb-1 text-sm">
                                    <span className="text-gray-300">Assets with Points</span>
                                    <span className="font-bold text-white">{Data_Completeness.Assets_With_Points_Pct}%</span>
                                </div>
                                <div className="w-full bg-gray-700 rounded-full h-2.5">
                                    <div className="bg-orange-500 h-2.5 rounded-full" style={{ width: `${Data_Completeness.Assets_With_Points_Pct}%` }}></div>
                                </div>
                            </div>
                             <div>
                                <div className="flex justify-between mb-1 text-sm">
                                    <span className="text-gray-300">Assets with Critical Points</span>
                                    <span className="font-bold text-white">{Data_Completeness.Assets_With_Critical_Points_Pct}%</span>
                                </div>
                                <div className="w-full bg-gray-700 rounded-full h-2.5">
                                    <div className="bg-red-500 h-2.5 rounded-full" style={{ width: `${Data_Completeness.Assets_With_Critical_Points_Pct}%` }}></div>
                                </div>
                            </div>
                         </div>
                    </div>
                </main>
            </div>
        </div>
    );
};