import React from 'react';
import { SearchBar } from './SearchBar';
import { CriticalityFilter } from './CriticalityFilter';
import type { DiagramNodeData } from '../types';

interface AppHeaderProps {
    allNodes: DiagramNodeData[];
    onSearchSelection: (node: DiagramNodeData) => void;
    criticalityFilter: string;
    setCriticalityFilter: (filter: string) => void;
    onOpenUploader: () => void;
    onOpenReport: () => void;
    onOpenSummary: () => void;
}

export const AppHeader: React.FC<AppHeaderProps> = ({
    allNodes,
    onSearchSelection,
    criticalityFilter,
    setCriticalityFilter,
    onOpenUploader,
    onOpenReport,
    onOpenSummary
}) => {
    return (
        <header className="flex items-center justify-between p-2 pl-4 bg-gray-800/50 backdrop-blur-sm border-b border-gray-700 z-30 space-x-4 shrink-0">
            <h1 className="text-xl font-bold whitespace-nowrap">HVAC Topology Orchestrator</h1>
            <SearchBar nodes={allNodes} onSelect={onSearchSelection} />
            <div className="flex items-center space-x-2">
                <CriticalityFilter filter={criticalityFilter} setFilter={setCriticalityFilter} />
                <button onClick={onOpenUploader} className="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors">Load Data</button>
                <button onClick={onOpenReport} className="bg-gray-700 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors">Validation Report</button>
                <button onClick={onOpenSummary} className="bg-gray-700 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors">Readiness Summary</button>
            </div>
        </header>
    );
};
