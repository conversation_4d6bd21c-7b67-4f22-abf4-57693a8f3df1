

import React, { useEffect, useState } from 'react';
import type { DiagramNodeData, ControlStrategies } from '../types';

interface SidePanelProps {
    nodeData: DiagramNodeData | null;
    isOpen: boolean;
    onClose: () => void;
    onGenerateDashboard: () => void;
    controlStrategies?: ControlStrategies;
}

const DetailItem: React.FC<{ label: string; value: React.ReactNode }> = ({ label, value }) => (
    <div className="grid grid-cols-3 gap-2 border-b border-gray-700/50 py-2">
        <span className="text-gray-400 font-medium col-span-1">{label}</span>
        <span className="text-white font-semibold col-span-2">{value}</span>
    </div>
);

export const SidePanel: React.FC<SidePanelProps> = ({ nodeData, isOpen, onClose, onGenerateDashboard, controlStrategies }) => {
    const [contentKey, setContentKey] = useState(0);

    useEffect(() => {
        const styleId = 'sidepanel-animation-style';
        if (document.getElementById(styleId)) {
            return;
        }
        const style = document.createElement('style');
        style.id = styleId;
        style.innerHTML = `
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
`;
        document.head.appendChild(style);
    }, []);

    useEffect(() => {
      // Change key to re-trigger animation on node change
      setContentKey(prev => prev + 1);
    }, [nodeData]);

    const renderData = (data: Record<string, any>) => {
        return Object.entries(data).map(([key, value]) => {
            if (key === 'Points' && Array.isArray(value)) {
                return <DetailItem key={key} label={key} value={<span className="text-blue-300">{value.length} Points</span>} />;
            }
            if (typeof value === 'object' && value !== null) {
                return (
                    <div key={key} className="border-b border-gray-700/50 py-2">
                        <span className="text-gray-400 font-medium">{key}:</span>
                        <pre className="text-xs p-2 bg-gray-800 rounded mt-1 overflow-x-auto text-amber-200">
                            <code>{JSON.stringify(value, null, 2)}</code>
                        </pre>
                    </div>
                );
            }
            return <DetailItem key={key} label={key} value={String(value)} />;
        });
    };
    
    const currentStrategy = nodeData && controlStrategies ? controlStrategies[nodeData.key] : null;
    
    return (
        <>
            <aside className={`w-full sm:w-[400px] bg-gray-900/80 backdrop-blur-md h-screen overflow-y-auto p-4 border-l border-gray-700 transition-transform duration-500 ease-in-out transform ${isOpen ? 'translate-x-0' : 'translate-x-full'} absolute sm:relative right-0 top-0 z-20 shadow-2xl`}>
                <div className="flex justify-between items-center mb-4 pb-2 border-b border-gray-600">
                    <h2 className="text-2xl font-bold text-white">Details</h2>
                    <button onClick={onClose} className="text-gray-400 hover:text-white text-3xl leading-none transition-colors">&times;</button>
                </div>
                <div key={contentKey} className="animate-[fadeIn_0.5s_ease-in-out]">
                    {nodeData ? (
                        <div>
                            <h3 className="text-xl font-semibold text-blue-300 mb-2 break-words">{nodeData.name}</h3>
                            <p className="text-sm bg-gray-700/50 inline-block px-3 py-1 rounded-full mb-4 font-mono tracking-tight">{nodeData.category}</p>
                            
                            {currentStrategy && (
                                <div className="my-4 p-3 bg-indigo-600/20 border border-indigo-500/50 rounded-lg shadow-inner">
                                    <p className="text-xs text-indigo-300 font-semibold tracking-wider uppercase">Active Strategy</p>
                                    <p className="text-lg font-bold text-white">{currentStrategy}</p>
                                </div>
                            )}

                            {(nodeData.category === 'AHU' || nodeData.category === 'Zone') && (
                                <div className="my-4">
                                    <button
                                        onClick={onGenerateDashboard}
                                        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 shadow-lg hover:shadow-blue-500/50"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M10 2a1 1 0 00-1 1v1a1 1 0 002 0V3a1 1 0 00-1-1zM4 11a1 1 0 011 1v1a1 1 0 01-2 0v-1a1 1 0 011-1zM15 11a1 1 0 011 1v1a1 1 0 01-2 0v-1a1 1 0 011-1zM10 16a1 1 0 011 1v1a1 1 0 01-2 0v-1a1 1 0 011-1zM3 6a1 1 0 011-1h1a1 1 0 110 2H4a1 1 0 01-1-1zM14 5a1 1 0 110 2h1a1 1 0 110-2h-1zM7 3a1 1 0 00-1 1v1a1 1 0 102 0V4a1 1 0 00-1-1zM7 15a1 1 0 00-1 1v1a1 1 0 102 0v-1a1 1 0 00-1-1zM12 7a1 1 0 100 2h1a1 1 0 100-2h-1z" />
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0 2a10 10 0 100-20 10 10 0 000 20z" clipRule="evenodd" />
                                        </svg>
                                        <span>Generate Dashboard</span>
                                    </button>
                                </div>
                            )}

                            <div className="space-y-2 text-sm">
                            {renderData(nodeData.data)}
                            </div>
                        </div>
                    ) : (
                        <div className="flex flex-col items-center justify-center h-full text-center text-gray-500 pt-20">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <p>Select a component in the diagram to view its properties.</p>
                        </div>
                    )}
                </div>
            </aside>
        </>
    );
};