import React from 'react';
import type { ValidationReportData, ValidationCheckSection, ValidationIssue } from '../types';

interface ValidationReportProps {
    report: ValidationReportData;
    onClose: () => void;
}

const getStatusClasses = (status: 'Passed' | 'Warning' | 'Failed' | string) => {
    switch (status.toLowerCase()) {
        case 'passed':
            return {
                bg: 'bg-green-500/10',
                text: 'text-green-400',
                border: 'border-green-500/50',
                badgeBg: 'bg-green-500/20'
            };
        case 'warning':
        case 'passed with warnings':
            return {
                bg: 'bg-orange-500/10',
                text: 'text-orange-400',
                border: 'border-orange-500/50',
                badgeBg: 'bg-orange-500/20'
            };
        case 'failed':
            return {
                bg: 'bg-red-500/10',
                text: 'text-red-400',
                border: 'border-red-500/50',
                badgeBg: 'bg-red-500/20'
            };
        default:
            return {
                bg: 'bg-gray-700/20',
                text: 'text-gray-300',
                border: 'border-gray-600',
                badgeBg: 'bg-gray-700/40'
            };
    }
};

const StatusBadge: React.FC<{ status: 'Passed' | 'Warning' | 'Failed' | string }> = ({ status }) => {
    const classes = getStatusClasses(status);
    return (
        <span className={`px-3 py-1 text-xs font-semibold rounded-full ${classes.badgeBg} ${classes.text}`}>{status}</span>
    );
};

const IssueCard: React.FC<{ issue: ValidationIssue }> = ({ issue }) => {
    const classes = getStatusClasses('Warning');
    return (
        <div className={`bg-gray-800/60 p-4 rounded-lg border-l-4 ${classes.border}`}>
            <h4 className="font-semibold text-gray-100">{issue.Check}</h4>
            <p className="text-gray-400 text-sm mt-1">{issue.Message}</p>
            {issue.Count !== undefined && <p className="text-xs text-gray-500 mt-2">Count: <span className="font-mono text-cyan-400">{issue.Count}</span></p>}
            {issue.Example_Assets && (
                <div className="mt-2">
                    <p className="text-xs text-gray-500">Examples:</p>
                    <div className="flex flex-wrap gap-1 mt-1">
                        {issue.Example_Assets.map(asset => <span key={asset} className="text-xs font-mono bg-gray-700 text-amber-300 px-2 py-0.5 rounded">{asset}</span>)}
                    </div>
                </div>
            )}
             {issue.Sources && (
                <div className="mt-2">
                    <p className="text-xs text-gray-500">Sources:</p>
                    <div className="flex flex-wrap gap-1 mt-1">
                        {issue.Sources.map(source => <span key={source} className="text-xs font-mono bg-gray-700 text-purple-300 px-2 py-0.5 rounded">{source}</span>)}
                    </div>
                </div>
            )}
        </div>
    );
};

const ReportSection: React.FC<{ title: string; data: ValidationCheckSection }> = ({ title, data }) => (
    <div className="mb-6">
        <div className="flex justify-between items-center mb-3">
            <h3 className="text-lg font-bold text-blue-300">{title}</h3>
            <StatusBadge status={data.Status} />
        </div>
        {data.Details && <p className="bg-gray-800/60 p-3 rounded-md text-sm text-gray-300">{data.Details}</p>}
        {data.Issues && (
            <div className="space-y-3">
                {data.Issues.map((issue, index) => <IssueCard key={index} issue={issue} />)}
            </div>
        )}
    </div>
);


export const ValidationReport: React.FC<ValidationReportProps> = ({ report, onClose }) => {
    const summaryClasses = getStatusClasses(report.Summary.Status);
    return (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-[fadeIn_0.3s_ease-out]">
            <div className="bg-gray-900 border border-gray-700 rounded-xl w-full max-w-4xl h-full max-h-[90vh] flex flex-col shadow-2xl">
                <header className="flex justify-between items-center p-4 border-b border-gray-700 sticky top-0 bg-gray-900/80 backdrop-blur-sm">
                    <h2 className="text-xl font-bold text-white">Topology Validation Report</h2>
                    <button onClick={onClose} className="text-gray-400 hover:text-white text-3xl leading-none transition-colors">&times;</button>
                </header>
                <main className="p-6 overflow-y-auto">
                    <div className={`p-4 rounded-lg mb-6 border ${summaryClasses.bg} ${summaryClasses.border}`}>
                        <div className="flex justify-between items-center mb-2">
                             <h3 className="text-lg font-bold text-gray-100">Summary</h3>
                             <StatusBadge status={report.Summary.Status} />
                        </div>
                        <p className={`text-sm ${summaryClasses.text}`}>{report.Summary.Overall_Message}</p>
                        <div className="flex space-x-4 text-sm mt-3 pt-3 border-t border-gray-700/50">
                            <span>Total Checks: <span className="font-bold text-white">{report.Summary.Total_Checks}</span></span>
                            <span className="text-green-400">Passed: <span className="font-bold">{report.Summary.Passed_Count}</span></span>
                            <span className="text-orange-400">Warnings: <span className="font-bold">{report.Summary.Warning_Count}</span></span>
                            <span className="text-red-400">Failed: <span className="font-bold">{report.Summary.Failed_Count}</span></span>
                        </div>
                    </div>

                    <ReportSection title="Schema Compliance" data={report.Schema_Compliance} />
                    <ReportSection title="Connectivity Checks" data={report.Connectivity_Checks} />
                    <ReportSection title="Point Mapping Checks" data={report.Point_Mapping_Checks} />
                    <ReportSection title="Data Source Checks" data={report.Data_Source_Checks} />
                </main>
            </div>
        </div>
    );
};
