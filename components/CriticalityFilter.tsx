import React from 'react';

interface CriticalityFilterProps {
    filter: string;
    setFilter: (f: string) => void;
}

export const CriticalityFilter: React.FC<CriticalityFilterProps> = ({ filter, setFilter }) => {
    const filters = [
        { name: 'All', color: 'gray' },
        { name: 'High', color: 'red' },
        { name: 'Medium', color: 'orange' },
        { name: 'Low', color: 'green' }
    ];

    const colors: { [key: string]: { base: string, active: string } } = {
        gray: { base: 'bg-gray-700 hover:bg-gray-600', active: 'bg-gray-500 ring-2 ring-gray-300' },
        red: { base: 'bg-red-700 hover:bg-red-600', active: 'bg-red-500 ring-2 ring-red-200' },
        orange: { base: 'bg-orange-700 hover:bg-orange-600', active: 'bg-orange-500 ring-2 ring-orange-200' },
        green: { base: 'bg-green-700 hover:bg-green-600', active: 'bg-green-500 ring-2 ring-green-200' }
    };

    return (
        <div className="bg-gray-800 border border-gray-600 rounded-lg p-1 flex items-center space-x-1">
            {filters.map(f => (
                <button
                    key={f.name}
                    onClick={() => setFilter(f.name)}
                    className={`px-3 py-1 rounded-md text-sm font-semibold transition-all duration-200 ${filter === f.name ? colors[f.color].active : colors[f.color].base} text-white focus:outline-none`}
                >
                    {f.name}
                </button>
            ))}
        </div>
    );
};
