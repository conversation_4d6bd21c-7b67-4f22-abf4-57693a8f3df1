import React, { useState, useEffect, useMemo } from 'react';
import type { DiagramNodeData, Point, Equipment } from '../types';

// Helper function to generate mock values with color coding
const generateMockValue = (pointName: string): { value: string; color: string } => {
    const name = pointName.toLowerCase();
    if (name.includes('temp')) {
        const val = (Math.random() * 7 + 18).toFixed(1); // 18.0 - 25.0
        return { value: `${val} °C`, color: val > '24' ? 'text-orange-400' : 'text-cyan-400' };
    }
    if (name.includes('setpoint')) {
        const val = (Math.random() * 3 + 20).toFixed(1); // 20.0 - 23.0
        return { value: `${val} °C`, color: 'text-green-400' };
    }
    if (name.includes('demand') || name.includes('vsd')) {
        const val = (Math.random() * 100).toFixed(0);
        return { value: `${val} %`, color: val > '85' ? 'text-red-400' : 'text-yellow-400' };
    }
    if (name.includes('pressure')) {
        const val = (Math.random() * 450 + 50).toFixed(0);
        return { value: `${val} Pa`, color: 'text-purple-400' };
    }
    if (name.includes('humidity')) {
        const val = (Math.random() * 20 + 40).toFixed(0);
        return { value: `${val} %RH`, color: 'text-blue-400' };
    }
    if (name.includes('co2')) {
        const val = (Math.random() * 800 + 400).toFixed(0);
        return { value: `${val} ppm`, color: val > '1000' ? 'text-yellow-500' : 'text-gray-300' };
    }
    if (name.includes('status')) {
        return { value: Math.random() > 0.1 ? 'On' : 'Off', color: 'text-teal-400' };
    }
     if (name.includes('fault')) {
        return { value: Math.random() > 0.95 ? 'Fault' : 'Ok', color: Math.random() > 0.95 ? 'text-red-500' : 'text-green-500' };
    }
    if (name.includes('available')) {
        return { value: 'Yes', color: 'text-green-400' };
    }
    return { value: 'N/A', color: 'text-gray-500' };
};

// SVG Icons for different point types
const ThermometerIcon = () => <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16V4a1 1 0 00-1-1H9a1 1 0 00-1 1v12a5 5 0 005 5h1a5 5 0 005-5v-1a1 1 0 00-1-1h-2a1 1 0 00-1 1v1a3 3 0 01-3 3h-1a3 3 0 01-3-3V5a1 1 0 011-1h2a1 1 0 011 1v11" /></svg>;
const GaugeIcon = () => <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>;
const StatusIcon = () => <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>;
const AlertIcon = () => <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>;

const getIconForPoint = (pointName: string) => {
    const name = pointName.toLowerCase();
    if (name.includes('temp') || name.includes('setpoint')) return <ThermometerIcon />;
    if (name.includes('demand') || name.includes('pressure') || name.includes('vsd') || name.includes('co2') || name.includes('humidity')) return <GaugeIcon />;
    if (name.includes('fault')) return <AlertIcon/>;
    return <StatusIcon />;
};

interface PointCardProps {
    point: Point;
    value: { value: string; color: string };
}

const PointCard: React.FC<PointCardProps> = ({ point, value }) => (
    <div className="bg-gray-800/50 rounded-lg p-4 flex flex-col justify-between shadow-lg border border-gray-700 hover:border-blue-500 transition-all duration-200">
        <div>
            <div className="flex justify-between items-start text-gray-400">
                <p className="font-semibold text-sm break-words max-w-[80%]">{point.Name}</p>
                {getIconForPoint(point.Name)}
            </div>
        </div>
        <div className="text-right mt-2">
            <p className={`text-3xl font-bold ${value.color}`}>{value.value.split(' ')[0]}</p>
            <p className="text-sm text-gray-500">{value.value.split(' ').slice(1).join(' ')}</p>
        </div>
    </div>
);


interface DashboardProps {
    nodeData: DiagramNodeData;
    onClose: () => void;
}

export const Dashboard: React.FC<DashboardProps> = ({ nodeData, onClose }) => {
    const points = useMemo(() => {
        const extractedPoints: Point[] = [];
        if (nodeData.category === 'AHU') {
            extractedPoints.push(...(nodeData.data.Points || []));
        } else if (nodeData.category === 'Zone') {
            (nodeData.data.Equipment as Equipment[] || []).forEach(equip => {
                extractedPoints.push(...(equip.Points || []));
            });
        }
        const keyPointKeywords = ['temp', 'setpoint', 'demand', 'pressure', 'status', 'fault', 'co2', 'humidity'];
        return extractedPoints.filter(p => keyPointKeywords.some(kw => p.Name.toLowerCase().includes(kw)));
    }, [nodeData]);

    const [pointValues, setPointValues] = useState<Record<string, { value: string; color: string }>>({});

    useEffect(() => {
        const updateValues = () => {
            const newValues: Record<string, { value: string; color: string }> = {};
            points.forEach(point => {
                newValues[point.Name] = generateMockValue(point.Name);
            });
            setPointValues(newValues);
        };

        updateValues(); // Initial values
        const intervalId = setInterval(updateValues, 3000); // Update every 3 seconds

        return () => clearInterval(intervalId);
    }, [points]);

    return (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-[fadeIn_0.3s_ease-out]">
            <div className="bg-gray-900 border border-gray-700 rounded-xl w-full max-w-4xl h-full max-h-[90vh] flex flex-col shadow-2xl">
                <header className="flex justify-between items-center p-4 border-b border-gray-700">
                    <h2 className="text-xl font-bold text-white">
                        <span className="text-blue-400">{nodeData.category}</span> Dashboard: {nodeData.name}
                    </h2>
                    <button onClick={onClose} className="text-gray-400 hover:text-white text-3xl leading-none transition-colors">&times;</button>
                </header>
                <main className="p-6 overflow-y-auto">
                    {points.length > 0 ? (
                         <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            {points.map(point => (
                                <PointCard key={point.Name} point={point} value={pointValues[point.Name] || { value: '...', color: 'text-gray-500' }} />
                            ))}
                        </div>
                    ) : (
                        <div className="text-center text-gray-500 py-20">
                            <p>No key data points found for this component.</p>
                        </div>
                    )}
                </main>
            </div>
        </div>
    );
};