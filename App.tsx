// FIX: Add declaration for the 'go' namespace to resolve TypeScript errors.
declare const go: any;

import React, { useState, useMemo, useRef } from 'react';
import { DiagramWrapper } from './components/Diagram';
import { SidePanel } from './components/SidePanel';
import { Chatbot } from './components/Chatbot';
import { AppHeader } from './components/AppHeader';
import { AppModals } from './components/AppModals';
import { useGoJSLoader } from './hooks/useGoJSLoader';
import { useChatbot } from './hooks/useChatbot';
import { transformTopologyToModel } from './utils/dataTransformer';
import { calculateShortestPath } from './utils/dijkstra';
import { masterTopology as initialTopology } from './data/topology';
import { validationReport as initialReport } from './data/validationReport';
import { controlReadinessSummary as initialSummary } from './data/controlReadinessSummary';
import { ontologyGraph as initialOntology } from './data/ontology';
import type { DiagramNodeData, MasterTopology, ValidationReportData, ControlReadinessSummaryData, AiActionCommand, ControlStrategies, OntologyGraph, DijkstraPath } from './types';

const App: React.FC = () => {
    const isLoadingDiagram = useGoJSLoader();
    
    // Core application state
    const [selectedNode, setSelectedNode] = useState<DiagramNodeData | null>(null);
    const [isSidePanelOpen, setIsSidePanelOpen] = useState(false);
    const [criticalityFilter, setCriticalityFilter] = useState('All');
    const [controlStrategies, setControlStrategies] = useState<ControlStrategies>({});
    const [highlightedPath, setHighlightedPath] = useState<DijkstraPath | null>(null);
    const diagramRef = useRef<any>(null);

    // Data state
    const [topologyData, setTopologyData] = useState<MasterTopology>(initialTopology);
    const [reportData, setReportData] = useState<ValidationReportData>(initialReport);
    const [summaryData, setSummaryData] = useState<ControlReadinessSummaryData>(initialSummary);
    const [ontologyData, setOntologyData] = useState<OntologyGraph>(initialOntology);
    
    // Modal visibility state
    const [isReportOpen, setIsReportOpen] = useState(false);
    const [isSummaryOpen, setIsSummaryOpen] = useState(false);
    const [isDashboardOpen, setIsDashboardOpen] = useState(false);
    const [isUploaderOpen, setIsUploaderOpen] = useState(false);

    const modelData = useMemo(() => transformTopologyToModel(topologyData), [topologyData]);
    const allNodes = useMemo(() => modelData.nodes, [modelData]);

    const executeAiAction = (command: AiActionCommand) => {
        const { action, payload } = command;
        // Clear any existing path highlights when a new action is taken
        setHighlightedPath(null);

        switch (action) {
            case 'SELECT_NODE':
                const nodeToSelect = allNodes.find(n => n.key === payload.key);
                if (nodeToSelect) {
                    handleSearchSelection(nodeToSelect);
                }
                break;
            case 'SET_FILTER':
                if (['All', 'High', 'Medium', 'Low'].includes(payload.filter)) {
                    setCriticalityFilter(payload.filter);
                }
                break;
            case 'OPEN_DASHBOARD':
                 const nodeForDashboard = allNodes.find(n => n.key === payload.key);
                 if (nodeForDashboard && (nodeForDashboard.category === 'AHU' || nodeForDashboard.category === 'Zone')) {
                     setSelectedNode(nodeForDashboard);
                     setIsSidePanelOpen(true);
                     setIsDashboardOpen(true);
                 }
                break;
            case 'UPDATE_CONTROL_STRATEGY':
                if (payload.equipId && payload.newStrategy) {
                    setControlStrategies(prev => ({
                        ...prev,
                        [payload.equipId]: payload.newStrategy
                    }));
                }
                break;
            case 'CALCULATE_DIJKSTRA_PATH':
                if (payload.startNodeKey && payload.endNodeKey) {
                    const path = calculateShortestPath(modelData.nodes, modelData.links, payload.startNodeKey, payload.endNodeKey);
                    setHighlightedPath(path);
                }
                break;
            default:
                console.warn("Unknown AI action:", action);
        }
    };

    const {
        isChatbotOpen,
        setIsChatbotOpen,
        chatMessages,
        isBotLoading,
        handleSendMessage,
        resetChat
    } = useChatbot(topologyData, reportData, summaryData, ontologyData, executeAiAction);


    const handleNodeSelection = (nodeData: DiagramNodeData | null) => {
        setSelectedNode(nodeData);
        setIsSidePanelOpen(!!nodeData);
        setHighlightedPath(null); // Clear path on new selection
    };

    const handleSearchSelection = (node: DiagramNodeData) => {
        if (diagramRef.current) {
            const diagramNode = diagramRef.current.findNodeForKey(node.key);
            if (diagramNode) {
                diagramRef.current.select(diagramNode);
                diagramRef.current.centerRect(diagramNode.actualBounds);
                setSelectedNode(node);
                setIsSidePanelOpen(true);
            }
        }
    };

    const handleDataUpload = (newData: { topology: MasterTopology; report: ValidationReportData; summary: ControlReadinessSummaryData; ontology: OntologyGraph; }) => {
        setTopologyData(newData.topology);
        setReportData(newData.report);
        setSummaryData(newData.summary);
        setOntologyData(newData.ontology);
        setIsUploaderOpen(false);
        resetChat();
    };

    if (isLoadingDiagram) {
        return (
            <div className="flex items-center justify-center h-screen bg-gray-900 text-gray-300">
                <div className="text-xl font-semibold">Processing Topology...</div>
            </div>
        );
    }

    return (
        <div className="h-screen w-screen flex flex-col bg-gray-900 text-gray-200">
            <AppHeader
                allNodes={allNodes}
                onSearchSelection={handleSearchSelection}
                criticalityFilter={criticalityFilter}
                setCriticalityFilter={setCriticalityFilter}
                onOpenUploader={() => setIsUploaderOpen(true)}
                onOpenReport={() => setIsReportOpen(true)}
                onOpenSummary={() => setIsSummaryOpen(true)}
            />

            <main className="flex-grow flex relative overflow-hidden">
                <DiagramWrapper
                    ref={diagramRef}
                    nodeDataArray={modelData.nodes}
                    linkDataArray={modelData.links}
                    onNodeSelection={handleNodeSelection}
                    criticalityFilter={criticalityFilter}
                    highlightedPath={highlightedPath}
                />
                <SidePanel
                    nodeData={selectedNode}
                    isOpen={isSidePanelOpen}
                    onClose={() => setIsSidePanelOpen(false)}
                    onGenerateDashboard={() => setIsDashboardOpen(true)}
                    controlStrategies={controlStrategies}
                />
            </main>

            <AppModals
                isUploaderOpen={isUploaderOpen}
                onCloseUploader={() => setIsUploaderOpen(false)}
                onDataUpload={handleDataUpload}
                isReportOpen={isReportOpen}
                onCloseReport={() => setIsReportOpen(false)}
                reportData={reportData}
                isSummaryOpen={isSummaryOpen}
                onCloseSummary={() => setIsSummaryOpen(false)}
                summaryData={summaryData}
                isDashboardOpen={isDashboardOpen}
                onCloseDashboard={() => setIsDashboardOpen(false)}
                selectedNode={selectedNode}
            />
            
            <div className="fixed bottom-6 right-6 z-50">
                <button
                    onClick={() => setIsChatbotOpen(!isChatbotOpen)}
                    className="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-4 shadow-lg transition-all duration-300 ease-in-out hover:scale-110 flex items-center justify-center w-16 h-16"
                    aria-label="Toggle Chatbot"
                >
                    <div className="relative w-8 h-8">
                        <svg xmlns="http://www.w3.org/2000/svg" className={`absolute inset-0 w-8 h-8 transition-all duration-300 ease-in-out ${isChatbotOpen ? 'opacity-0 transform -rotate-90 scale-50' : 'opacity-100 transform rotate-0 scale-100'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                            <path strokeLinecap="round" strokeLinejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" className={`absolute inset-0 w-8 h-8 transition-all duration-300 ease-in-out ${isChatbotOpen ? 'opacity-100 transform rotate-0 scale-100' : 'opacity-0 transform rotate-90 scale-50'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </div>
                </button>
            </div>
            
            <Chatbot
                isOpen={isChatbotOpen}
                onClose={() => setIsChatbotOpen(false)}
                messages={chatMessages}
                onSendMessage={handleSendMessage}
                isLoading={isBotLoading}
            />
        </div>
    );
};

export default App;